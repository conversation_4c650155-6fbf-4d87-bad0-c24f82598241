#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1248496 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (./open/src/hotspot/share/memory/arena.cpp:197), pid=17616, tid=20100
#
# JRE version: Java(TM) SE Runtime Environment 18.9 (11.0.25+9) (build 11.0.25+9-LTS-256)
# Java VM: Java HotSpot(TM) 64-Bit Server VM 18.9 (11.0.25+9-LTS-256, mixed mode, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=512m -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: Intel(R) Core(TM) i7-8650U CPU @ 1.90GHz, 8 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Tue Jul  1 12:43:02 2025 E. Africa Standard Time elapsed time: 29.573156 seconds (0d 0h 0m 29s)

---------------  T H R E A D  ---------------

Current thread (0x000001b008283000):  JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=20100, stack(0x00000032abe00000,0x00000032abf00000)]


Current CompileTask:
C2:  29573 4365       4       groovy.lang.MetaClassImpl$1MOPIter::methodNameAction (325 bytes)

Stack: [0x00000032abe00000,0x00000032abf00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x65824a]
V  [jvm.dll+0x79ae8f]
V  [jvm.dll+0x79c559]
V  [jvm.dll+0x79cc03]
V  [jvm.dll+0x255785]
V  [jvm.dll+0xb312c]
V  [jvm.dll+0xb373c]
V  [jvm.dll+0x35fa2f]
V  [jvm.dll+0x1a0f62]
V  [jvm.dll+0x20f27d]
V  [jvm.dll+0x20e0fd]
V  [jvm.dll+0x18b381]
V  [jvm.dll+0x21e774]
V  [jvm.dll+0x21ca9c]
V  [jvm.dll+0x75ed51]
V  [jvm.dll+0x757674]
V  [jvm.dll+0x6570f5]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001b008c44610, length=40, elements={
0x000001b061e1b000, 0x000001b000f85000, 0x000001b000f86800, 0x000001b000fa3800,
0x000001b000fa4800, 0x000001b000fa7800, 0x000001b000ff7800, 0x000001b00100d800,
0x000001b00100e800, 0x000001b00116d000, 0x000001b002a3b800, 0x000001b001be9000,
0x000001b00247c800, 0x000001b002c62000, 0x000001b001821000, 0x000001b002155800,
0x000001b0022e4800, 0x000001b001cdf000, 0x000001b002c2d800, 0x000001b0029c5800,
0x000001b001be4000, 0x000001b001be3000, 0x000001b001be5000, 0x000001b001093000,
0x000001b001be5800, 0x000001b009e75000, 0x000001b009e75800, 0x000001b009e76800,
0x000001b009e74000, 0x000001b009e79800, 0x000001b009e77800, 0x000001b009e78000,
0x000001b009e7a800, 0x000001b009e7b800, 0x000001b009e79000, 0x000001b009e7d000,
0x000001b009e7e800, 0x000001b009e7c000, 0x000001b009e7f800, 0x000001b008283000
}

Java Threads: ( => current thread )
  0x000001b061e1b000 JavaThread "main" [_thread_blocked, id=9356, stack(0x00000032a8800000,0x00000032a8900000)]
  0x000001b000f85000 JavaThread "Reference Handler" daemon [_thread_blocked, id=3924, stack(0x00000032a8f00000,0x00000032a9000000)]
  0x000001b000f86800 JavaThread "Finalizer" daemon [_thread_blocked, id=23560, stack(0x00000032a9000000,0x00000032a9100000)]
  0x000001b000fa3800 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=10824, stack(0x00000032a9100000,0x00000032a9200000)]
  0x000001b000fa4800 JavaThread "Attach Listener" daemon [_thread_blocked, id=25440, stack(0x00000032a9200000,0x00000032a9300000)]
  0x000001b000fa7800 JavaThread "Service Thread" daemon [_thread_blocked, id=16292, stack(0x00000032a9300000,0x00000032a9400000)]
  0x000001b000ff7800 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=20848, stack(0x00000032a9400000,0x00000032a9500000)]
  0x000001b00100d800 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=25832, stack(0x00000032a9500000,0x00000032a9600000)]
  0x000001b00100e800 JavaThread "Sweeper thread" daemon [_thread_blocked, id=12256, stack(0x00000032a9600000,0x00000032a9700000)]
  0x000001b00116d000 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=25616, stack(0x00000032a9700000,0x00000032a9800000)]
  0x000001b002a3b800 JavaThread "Daemon health stats" [_thread_blocked, id=20400, stack(0x00000032a9800000,0x00000032a9900000)]
  0x000001b001be9000 JavaThread "Incoming local TCP Connector on port 39299" [_thread_in_native, id=16588, stack(0x00000032a9a00000,0x00000032a9b00000)]
  0x000001b00247c800 JavaThread "Daemon periodic checks" [_thread_blocked, id=23100, stack(0x00000032aa100000,0x00000032aa200000)]
  0x000001b002c62000 JavaThread "Daemon" [_thread_blocked, id=24052, stack(0x00000032aa200000,0x00000032aa300000)]
  0x000001b001821000 JavaThread "Handler for socket connection from /127.0.0.1:39299 to /127.0.0.1:39300" [_thread_in_native, id=19612, stack(0x00000032aa300000,0x00000032aa400000)]
  0x000001b002155800 JavaThread "Cancel handler" [_thread_blocked, id=18692, stack(0x00000032aa000000,0x00000032aa100000)]
  0x000001b0022e4800 JavaThread "Daemon worker" [_thread_in_native, id=6844, stack(0x00000032aa400000,0x00000032aa500000)]
  0x000001b001cdf000 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:39299 to /127.0.0.1:39300" [_thread_blocked, id=11728, stack(0x00000032aa500000,0x00000032aa600000)]
  0x000001b002c2d800 JavaThread "Stdin handler" [_thread_blocked, id=15744, stack(0x00000032aa600000,0x00000032aa700000)]
  0x000001b0029c5800 JavaThread "Daemon client event forwarder" [_thread_blocked, id=11700, stack(0x00000032aa700000,0x00000032aa800000)]
  0x000001b001be4000 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=21308, stack(0x00000032aab00000,0x00000032aac00000)]
  0x000001b001be3000 JavaThread "File lock request listener" [_thread_in_native, id=26072, stack(0x00000032aac00000,0x00000032aad00000)]
  0x000001b001be5000 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)" [_thread_blocked, id=16612, stack(0x00000032aad00000,0x00000032aae00000)]
  0x000001b001093000 JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=6020, stack(0x00000032aae00000,0x00000032aaf00000)]
  0x000001b001be5800 JavaThread "Problems report writer" [_thread_blocked, id=1316, stack(0x00000032ab000000,0x00000032ab100000)]
  0x000001b009e75000 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\android\.gradle\8.12\fileHashes)" [_thread_blocked, id=25512, stack(0x00000032ab100000,0x00000032ab200000)]
  0x000001b009e75800 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=25144, stack(0x00000032ab200000,0x00000032ab300000)]
  0x000001b009e76800 JavaThread "File watcher server" daemon [_thread_in_native, id=14632, stack(0x00000032ab300000,0x00000032ab400000)]
  0x000001b009e74000 JavaThread "File watcher consumer" daemon [_thread_blocked, id=23924, stack(0x00000032ab400000,0x00000032ab500000)]
  0x000001b009e79800 JavaThread "jar transforms" [_thread_blocked, id=21828, stack(0x00000032ab500000,0x00000032ab600000)]
  0x000001b009e77800 JavaThread "jar transforms Thread 2" [_thread_blocked, id=6840, stack(0x00000032ab600000,0x00000032ab700000)]
  0x000001b009e78000 JavaThread "jar transforms Thread 3" [_thread_blocked, id=17028, stack(0x00000032ab700000,0x00000032ab800000)]
  0x000001b009e7a800 JavaThread "jar transforms Thread 4" [_thread_blocked, id=19844, stack(0x00000032ab800000,0x00000032ab900000)]
  0x000001b009e7b800 JavaThread "jar transforms Thread 5" [_thread_blocked, id=12708, stack(0x00000032ab900000,0x00000032aba00000)]
  0x000001b009e79000 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\android\.gradle\8.12\checksums)" [_thread_blocked, id=23632, stack(0x00000032aba00000,0x00000032abb00000)]
  0x000001b009e7d000 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)" [_thread_blocked, id=19152, stack(0x00000032abb00000,0x00000032abc00000)]
  0x000001b009e7e800 JavaThread "jar transforms Thread 6" [_thread_blocked, id=18452, stack(0x00000032abc00000,0x00000032abd00000)]
  0x000001b009e7c000 JavaThread "jar transforms Thread 7" [_thread_blocked, id=21960, stack(0x00000032aaf00000,0x00000032ab000000)]
  0x000001b009e7f800 JavaThread "jar transforms Thread 8" [_thread_blocked, id=1376, stack(0x00000032abd00000,0x00000032abe00000)]
=>0x000001b008283000 JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=20100, stack(0x00000032abe00000,0x00000032abf00000)]

Other Threads:
  0x000001b000f7e800 VMThread "VM Thread" [stack: 0x00000032a8e00000,0x00000032a8f00000] [id=22592]
  0x000001b00130c800 WatcherThread [stack: 0x00000032a9900000,0x00000032a9a00000] [id=25416]
  0x000001b061e31800 GCTaskThread "GC Thread#0" [stack: 0x00000032a8900000,0x00000032a8a00000] [id=25620]
  0x000001b001bd2800 GCTaskThread "GC Thread#1" [stack: 0x00000032a9b00000,0x00000032a9c00000] [id=26544]
  0x000001b0018bf000 GCTaskThread "GC Thread#2" [stack: 0x00000032a9c00000,0x00000032a9d00000] [id=23104]
  0x000001b0018f6000 GCTaskThread "GC Thread#3" [stack: 0x00000032a9d00000,0x00000032a9e00000] [id=16736]
  0x000001b001cdf800 GCTaskThread "GC Thread#4" [stack: 0x00000032a9e00000,0x00000032a9f00000] [id=13836]
  0x000001b001bea000 GCTaskThread "GC Thread#5" [stack: 0x00000032a9f00000,0x00000032aa000000] [id=22456]
  0x000001b002c2e800 GCTaskThread "GC Thread#6" [stack: 0x00000032aa800000,0x00000032aa900000] [id=21984]
  0x000001b0018db800 GCTaskThread "GC Thread#7" [stack: 0x00000032aa900000,0x00000032aaa00000] [id=11276]
  0x000001b061e69800 ConcurrentGCThread "G1 Main Marker" [stack: 0x00000032a8a00000,0x00000032a8b00000] [id=16756]
  0x000001b061e6f800 ConcurrentGCThread "G1 Conc#0" [stack: 0x00000032a8b00000,0x00000032a8c00000] [id=16204]
  0x000001b0029c2800 ConcurrentGCThread "G1 Conc#1" [stack: 0x00000032aaa00000,0x00000032aab00000] [id=23668]
  0x000001b0005f4800 ConcurrentGCThread "G1 Refine#0" [stack: 0x00000032a8c00000,0x00000032a8d00000] [id=25604]
  0x000001b0005f5000 ConcurrentGCThread "G1 Young RemSet Sampling" [stack: 0x00000032a8d00000,0x00000032a8e00000] [id=4020]

Threads with active compile tasks:
C2 CompilerThread0    29645 4198   !   4       java.lang.ClassLoader::loadClass (122 bytes)
C2 CompilerThread1    29645 4469       4       org.objectweb.asm.Frame::execute (2303 bytes)
C2 CompilerThread2    29645 4365       4       groovy.lang.MetaClassImpl$1MOPIter::methodNameAction (325 bytes)

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 528482304 Address: 0x0000000100000000

Heap:
 garbage-first heap   total 260096K, used 95627K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 71 young (72704K), 14 survivors (14336K)
 Metaspace       used 47824K, capacity 49237K, committed 49404K, reserved 559104K
  class space    used 6092K, capacity 6613K, committed 6652K, reserved 516096K
Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, A=archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%| O|  |TAMS 0x0000000080100000, 0x0000000080000000| Untracked 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%| O|  |TAMS 0x0000000080200000, 0x0000000080100000| Untracked 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HS|  |TAMS 0x0000000080300000, 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080400000, 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%|HC|  |TAMS 0x0000000080500000, 0x0000000080400000| Complete 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%|HS|  |TAMS 0x0000000080600000, 0x0000000080500000| Complete 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080700000, 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080800000, 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000, 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000, 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000, 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000, 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080d00000, 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000, 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080f00000, 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000, 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000, 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000, 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x00000000812ece00, 0x0000000081300000| 92%| O|  |TAMS 0x00000000812ece00, 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%|HS|  |TAMS 0x0000000081300000, 0x0000000081300000| Complete 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%|HS|  |TAMS 0x0000000081400000, 0x0000000081400000| Complete 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%|HC|  |TAMS 0x0000000081500000, 0x0000000081500000| Complete 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%|HS|  |TAMS 0x0000000081600000, 0x0000000081600000| Complete 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%|HC|  |TAMS 0x0000000081700000, 0x0000000081700000| Complete 
|  24|0x0000000081800000, 0x0000000081800000, 0x0000000081900000|  0%| F|  |TAMS 0x0000000081800000, 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081900000, 0x0000000081a00000|  0%| F|  |TAMS 0x0000000081900000, 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081a00000, 0x0000000081b00000|  0%| F|  |TAMS 0x0000000081a00000, 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081b00000, 0x0000000081c00000|  0%| F|  |TAMS 0x0000000081b00000, 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081c00000, 0x0000000081d00000|  0%| F|  |TAMS 0x0000000081c00000, 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081d00000, 0x0000000081e00000|  0%| F|  |TAMS 0x0000000081d00000, 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081e00000, 0x0000000081f00000|  0%| F|  |TAMS 0x0000000081e00000, 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000081f00000, 0x0000000082000000|  0%| F|  |TAMS 0x0000000081f00000, 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082000000, 0x0000000082100000|  0%| F|  |TAMS 0x0000000082000000, 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082100000, 0x0000000082200000|  0%| F|  |TAMS 0x0000000082100000, 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082200000, 0x0000000082300000|  0%| F|  |TAMS 0x0000000082200000, 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082300000, 0x0000000082400000|  0%| F|  |TAMS 0x0000000082300000, 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082400000, 0x0000000082500000|  0%| F|  |TAMS 0x0000000082400000, 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082500000, 0x0000000082600000|  0%| F|  |TAMS 0x0000000082500000, 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082600000, 0x0000000082700000|  0%| F|  |TAMS 0x0000000082600000, 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082700000, 0x0000000082800000|  0%| F|  |TAMS 0x0000000082700000, 0x0000000082700000| Untracked 
|  40|0x0000000082800000, 0x0000000082800000, 0x0000000082900000|  0%| F|  |TAMS 0x0000000082800000, 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082900000, 0x0000000082a00000|  0%| F|  |TAMS 0x0000000082900000, 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082a00000, 0x0000000082b00000|  0%| F|  |TAMS 0x0000000082a00000, 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082b00000, 0x0000000082c00000|  0%| F|  |TAMS 0x0000000082b00000, 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082c00000, 0x0000000082d00000|  0%| F|  |TAMS 0x0000000082c00000, 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082d00000, 0x0000000082e00000|  0%| F|  |TAMS 0x0000000082d00000, 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082e00000, 0x0000000082f00000|  0%| F|  |TAMS 0x0000000082e00000, 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000082f00000, 0x0000000083000000|  0%| F|  |TAMS 0x0000000082f00000, 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083000000, 0x0000000083100000|  0%| F|  |TAMS 0x0000000083000000, 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083100000, 0x0000000083200000|  0%| F|  |TAMS 0x0000000083100000, 0x0000000083100000| Untracked 
|  50|0x0000000083200000, 0x0000000083200000, 0x0000000083300000|  0%| F|  |TAMS 0x0000000083200000, 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083300000, 0x0000000083400000|  0%| F|  |TAMS 0x0000000083300000, 0x0000000083300000| Untracked 
|  52|0x0000000083400000, 0x0000000083400000, 0x0000000083500000|  0%| F|  |TAMS 0x0000000083400000, 0x0000000083400000| Untracked 
|  53|0x0000000083500000, 0x0000000083500000, 0x0000000083600000|  0%| F|  |TAMS 0x0000000083500000, 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x0000000083600000, 0x0000000083700000|  0%| F|  |TAMS 0x0000000083600000, 0x0000000083600000| Untracked 
|  55|0x0000000083700000, 0x0000000083700000, 0x0000000083800000|  0%| F|  |TAMS 0x0000000083700000, 0x0000000083700000| Untracked 
|  56|0x0000000083800000, 0x0000000083800000, 0x0000000083900000|  0%| F|  |TAMS 0x0000000083800000, 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083900000, 0x0000000083a00000|  0%| F|  |TAMS 0x0000000083900000, 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083a00000, 0x0000000083b00000|  0%| F|  |TAMS 0x0000000083a00000, 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083b00000, 0x0000000083c00000|  0%| F|  |TAMS 0x0000000083b00000, 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083c00000, 0x0000000083d00000|  0%| F|  |TAMS 0x0000000083c00000, 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083d00000, 0x0000000083e00000|  0%| F|  |TAMS 0x0000000083d00000, 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083e00000, 0x0000000083f00000|  0%| F|  |TAMS 0x0000000083e00000, 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000083f00000, 0x0000000084000000|  0%| F|  |TAMS 0x0000000083f00000, 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084000000, 0x0000000084100000|  0%| F|  |TAMS 0x0000000084000000, 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084100000, 0x0000000084200000|  0%| F|  |TAMS 0x0000000084100000, 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084200000, 0x0000000084300000|  0%| F|  |TAMS 0x0000000084200000, 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084300000, 0x0000000084400000|  0%| F|  |TAMS 0x0000000084300000, 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084400000, 0x0000000084500000|  0%| F|  |TAMS 0x0000000084400000, 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084500000, 0x0000000084600000|  0%| F|  |TAMS 0x0000000084500000, 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084600000, 0x0000000084700000|  0%| F|  |TAMS 0x0000000084600000, 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084700000, 0x0000000084800000|  0%| F|  |TAMS 0x0000000084700000, 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084800000, 0x0000000084900000|  0%| F|  |TAMS 0x0000000084800000, 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084900000, 0x0000000084a00000|  0%| F|  |TAMS 0x0000000084900000, 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084a00000, 0x0000000084b00000|  0%| F|  |TAMS 0x0000000084a00000, 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084b00000, 0x0000000084c00000|  0%| F|  |TAMS 0x0000000084b00000, 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084c00000, 0x0000000084d00000|  0%| F|  |TAMS 0x0000000084c00000, 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084d00000, 0x0000000084e00000|  0%| F|  |TAMS 0x0000000084d00000, 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084e00000, 0x0000000084f00000|  0%| F|  |TAMS 0x0000000084e00000, 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000084f00000, 0x0000000085000000|  0%| F|  |TAMS 0x0000000084f00000, 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085000000, 0x0000000085100000|  0%| F|  |TAMS 0x0000000085000000, 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085100000, 0x0000000085200000|  0%| F|  |TAMS 0x0000000085100000, 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085200000, 0x0000000085300000|  0%| F|  |TAMS 0x0000000085200000, 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085300000, 0x0000000085400000|  0%| F|  |TAMS 0x0000000085300000, 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085400000, 0x0000000085500000|  0%| F|  |TAMS 0x0000000085400000, 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085500000, 0x0000000085600000|  0%| F|  |TAMS 0x0000000085500000, 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085600000, 0x0000000085700000|  0%| F|  |TAMS 0x0000000085600000, 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085700000, 0x0000000085800000|  0%| F|  |TAMS 0x0000000085700000, 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085800000, 0x0000000085900000|  0%| F|  |TAMS 0x0000000085800000, 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085900000, 0x0000000085a00000|  0%| F|  |TAMS 0x0000000085900000, 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085a00000, 0x0000000085b00000|  0%| F|  |TAMS 0x0000000085a00000, 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085b00000, 0x0000000085c00000|  0%| F|  |TAMS 0x0000000085b00000, 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085c00000, 0x0000000085d00000|  0%| F|  |TAMS 0x0000000085c00000, 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085d00000, 0x0000000085e00000|  0%| F|  |TAMS 0x0000000085d00000, 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085e00000, 0x0000000085f00000|  0%| F|  |TAMS 0x0000000085e00000, 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000085f00000, 0x0000000086000000|  0%| F|  |TAMS 0x0000000085f00000, 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086000000, 0x0000000086100000|  0%| F|  |TAMS 0x0000000086000000, 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086100000, 0x0000000086200000|  0%| F|  |TAMS 0x0000000086100000, 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086200000, 0x0000000086300000|  0%| F|  |TAMS 0x0000000086200000, 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086300000, 0x0000000086400000|  0%| F|  |TAMS 0x0000000086300000, 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086400000, 0x0000000086500000|  0%| F|  |TAMS 0x0000000086400000, 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086500000, 0x0000000086600000|  0%| F|  |TAMS 0x0000000086500000, 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086600000, 0x0000000086700000|  0%| F|  |TAMS 0x0000000086600000, 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086700000, 0x0000000086800000|  0%| F|  |TAMS 0x0000000086700000, 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086800000, 0x0000000086900000|  0%| F|  |TAMS 0x0000000086800000, 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086900000, 0x0000000086a00000|  0%| F|  |TAMS 0x0000000086900000, 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086a00000, 0x0000000086b00000|  0%| F|  |TAMS 0x0000000086a00000, 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086b00000, 0x0000000086c00000|  0%| F|  |TAMS 0x0000000086b00000, 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086c00000, 0x0000000086d00000|  0%| F|  |TAMS 0x0000000086c00000, 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086d00000, 0x0000000086e00000|  0%| F|  |TAMS 0x0000000086d00000, 0x0000000086d00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086e00000, 0x0000000086f00000|  0%| F|  |TAMS 0x0000000086e00000, 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000086f00000, 0x0000000087000000|  0%| F|  |TAMS 0x0000000086f00000, 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087000000, 0x0000000087100000|  0%| F|  |TAMS 0x0000000087000000, 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087100000, 0x0000000087200000|  0%| F|  |TAMS 0x0000000087100000, 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087200000, 0x0000000087300000|  0%| F|  |TAMS 0x0000000087200000, 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087300000, 0x0000000087400000|  0%| F|  |TAMS 0x0000000087300000, 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087400000, 0x0000000087500000|  0%| F|  |TAMS 0x0000000087400000, 0x0000000087400000| Untracked 
| 117|0x0000000087500000, 0x0000000087500000, 0x0000000087600000|  0%| F|  |TAMS 0x0000000087500000, 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087600000, 0x0000000087700000|  0%| F|  |TAMS 0x0000000087600000, 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087700000, 0x0000000087800000|  0%| F|  |TAMS 0x0000000087700000, 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087800000, 0x0000000087900000|  0%| F|  |TAMS 0x0000000087800000, 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087976190, 0x0000000087a00000| 46%| S|CS|TAMS 0x0000000087900000, 0x0000000087900000| Complete 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| S|CS|TAMS 0x0000000087a00000, 0x0000000087a00000| Complete 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| S|CS|TAMS 0x0000000087b00000, 0x0000000087b00000| Complete 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| S|CS|TAMS 0x0000000087c00000, 0x0000000087c00000| Complete 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| S|CS|TAMS 0x0000000087d00000, 0x0000000087d00000| Complete 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| S|CS|TAMS 0x0000000087e00000, 0x0000000087e00000| Complete 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| S|CS|TAMS 0x0000000087f00000, 0x0000000087f00000| Complete 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%| S|CS|TAMS 0x0000000088000000, 0x0000000088000000| Complete 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| S|CS|TAMS 0x0000000088100000, 0x0000000088100000| Complete 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%| S|CS|TAMS 0x0000000088200000, 0x0000000088200000| Complete 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| S|CS|TAMS 0x0000000088300000, 0x0000000088300000| Complete 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%| S|CS|TAMS 0x0000000088400000, 0x0000000088400000| Complete 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%| S|CS|TAMS 0x0000000088500000, 0x0000000088500000| Complete 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%| S|CS|TAMS 0x0000000088600000, 0x0000000088600000| Complete 
| 135|0x0000000088700000, 0x0000000088700000, 0x0000000088800000|  0%| F|  |TAMS 0x0000000088700000, 0x0000000088700000| Untracked 
| 136|0x0000000088800000, 0x0000000088800000, 0x0000000088900000|  0%| F|  |TAMS 0x0000000088800000, 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088900000, 0x0000000088a00000|  0%| F|  |TAMS 0x0000000088900000, 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088a00000, 0x0000000088b00000|  0%| F|  |TAMS 0x0000000088a00000, 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088b00000, 0x0000000088c00000|  0%| F|  |TAMS 0x0000000088b00000, 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088c00000, 0x0000000088d00000|  0%| F|  |TAMS 0x0000000088c00000, 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088d00000, 0x0000000088e00000|  0%| F|  |TAMS 0x0000000088d00000, 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088e00000, 0x0000000088f00000|  0%| F|  |TAMS 0x0000000088e00000, 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000088f00000, 0x0000000089000000|  0%| F|  |TAMS 0x0000000088f00000, 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089000000, 0x0000000089100000|  0%| F|  |TAMS 0x0000000089000000, 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089100000, 0x0000000089200000|  0%| F|  |TAMS 0x0000000089100000, 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089200000, 0x0000000089300000|  0%| F|  |TAMS 0x0000000089200000, 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089300000, 0x0000000089400000|  0%| F|  |TAMS 0x0000000089300000, 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089400000, 0x0000000089500000|  0%| F|  |TAMS 0x0000000089400000, 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089500000, 0x0000000089600000|  0%| F|  |TAMS 0x0000000089500000, 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x0000000089600000, 0x0000000089700000|  0%| F|  |TAMS 0x0000000089600000, 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089700000, 0x0000000089800000|  0%| F|  |TAMS 0x0000000089700000, 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089800000, 0x0000000089900000|  0%| F|  |TAMS 0x0000000089800000, 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089900000, 0x0000000089a00000|  0%| F|  |TAMS 0x0000000089900000, 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089a00000, 0x0000000089b00000|  0%| F|  |TAMS 0x0000000089a00000, 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089b00000, 0x0000000089c00000|  0%| F|  |TAMS 0x0000000089b00000, 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089c00000, 0x0000000089d00000|  0%| F|  |TAMS 0x0000000089c00000, 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089d00000, 0x0000000089e00000|  0%| F|  |TAMS 0x0000000089d00000, 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089e00000, 0x0000000089f00000|  0%| F|  |TAMS 0x0000000089e00000, 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x0000000089f00000, 0x000000008a000000|  0%| F|  |TAMS 0x0000000089f00000, 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a000000, 0x000000008a100000|  0%| F|  |TAMS 0x000000008a000000, 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a100000, 0x000000008a200000|  0%| F|  |TAMS 0x000000008a100000, 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a200000, 0x000000008a300000|  0%| F|  |TAMS 0x000000008a200000, 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a300000, 0x000000008a400000|  0%| F|  |TAMS 0x000000008a300000, 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a400000, 0x000000008a500000|  0%| F|  |TAMS 0x000000008a400000, 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a500000, 0x000000008a600000|  0%| F|  |TAMS 0x000000008a500000, 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a600000, 0x000000008a700000|  0%| F|  |TAMS 0x000000008a600000, 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a700000, 0x000000008a800000|  0%| F|  |TAMS 0x000000008a700000, 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a800000, 0x000000008a900000|  0%| F|  |TAMS 0x000000008a800000, 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008a900000, 0x000000008aa00000|  0%| F|  |TAMS 0x000000008a900000, 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008aa00000, 0x000000008ab00000|  0%| F|  |TAMS 0x000000008aa00000, 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ab00000, 0x000000008ac00000|  0%| F|  |TAMS 0x000000008ab00000, 0x000000008ab00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ac00000, 0x000000008ad00000|  0%| F|  |TAMS 0x000000008ac00000, 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ad00000, 0x000000008ae00000|  0%| F|  |TAMS 0x000000008ad00000, 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008ae00000, 0x000000008af00000|  0%| F|  |TAMS 0x000000008ae00000, 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008af00000, 0x000000008b000000|  0%| F|  |TAMS 0x000000008af00000, 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b000000, 0x000000008b100000|  0%| F|  |TAMS 0x000000008b000000, 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b100000, 0x000000008b200000|  0%| F|  |TAMS 0x000000008b100000, 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b200000, 0x000000008b300000|  0%| F|  |TAMS 0x000000008b200000, 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b300000, 0x000000008b400000|  0%| F|  |TAMS 0x000000008b300000, 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b400000, 0x000000008b500000|  0%| F|  |TAMS 0x000000008b400000, 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b500000, 0x000000008b600000|  0%| F|  |TAMS 0x000000008b500000, 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b600000, 0x000000008b700000|  0%| F|  |TAMS 0x000000008b600000, 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b700000, 0x000000008b800000|  0%| F|  |TAMS 0x000000008b700000, 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b800000, 0x000000008b900000|  0%| F|  |TAMS 0x000000008b800000, 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008b900000, 0x000000008ba00000|  0%| F|  |TAMS 0x000000008b900000, 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008ba00000, 0x000000008bb00000|  0%| F|  |TAMS 0x000000008ba00000, 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bb00000, 0x000000008bc00000|  0%| F|  |TAMS 0x000000008bb00000, 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bc00000, 0x000000008bd00000|  0%| F|  |TAMS 0x000000008bc00000, 0x000000008bc00000| Untracked 
| 189|0x000000008bd00000, 0x000000008bd00000, 0x000000008be00000|  0%| F|  |TAMS 0x000000008bd00000, 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008be00000, 0x000000008bf00000|  0%| F|  |TAMS 0x000000008be00000, 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008bf00000, 0x000000008c000000|  0%| F|  |TAMS 0x000000008bf00000, 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c000000, 0x000000008c100000|  0%| F|  |TAMS 0x000000008c000000, 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c100000, 0x000000008c200000|  0%| F|  |TAMS 0x000000008c100000, 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c200000, 0x000000008c300000|  0%| F|  |TAMS 0x000000008c200000, 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c300000, 0x000000008c400000|  0%| F|  |TAMS 0x000000008c300000, 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c400000, 0x000000008c500000|  0%| F|  |TAMS 0x000000008c400000, 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%| E|  |TAMS 0x000000008c500000, 0x000000008c500000| Complete 
| 198|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%| E|CS|TAMS 0x000000008c600000, 0x000000008c600000| Complete 
| 199|0x000000008c700000, 0x000000008c800000, 0x000000008c800000|100%| E|CS|TAMS 0x000000008c700000, 0x000000008c700000| Complete 
| 200|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%| E|CS|TAMS 0x000000008c800000, 0x000000008c800000| Complete 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%| E|CS|TAMS 0x000000008c900000, 0x000000008c900000| Complete 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%| E|CS|TAMS 0x000000008ca00000, 0x000000008ca00000| Complete 
| 203|0x000000008cb00000, 0x000000008cc00000, 0x000000008cc00000|100%| E|CS|TAMS 0x000000008cb00000, 0x000000008cb00000| Complete 
| 204|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%| E|CS|TAMS 0x000000008cc00000, 0x000000008cc00000| Complete 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%| E|CS|TAMS 0x000000008cd00000, 0x000000008cd00000| Complete 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%| E|CS|TAMS 0x000000008ce00000, 0x000000008ce00000| Complete 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%| E|CS|TAMS 0x000000008cf00000, 0x000000008cf00000| Complete 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| E|CS|TAMS 0x000000008d000000, 0x000000008d000000| Complete 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| E|CS|TAMS 0x000000008d100000, 0x000000008d100000| Complete 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| E|CS|TAMS 0x000000008d200000, 0x000000008d200000| Complete 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| E|CS|TAMS 0x000000008d300000, 0x000000008d300000| Complete 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| E|CS|TAMS 0x000000008d400000, 0x000000008d400000| Complete 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| E|CS|TAMS 0x000000008d500000, 0x000000008d500000| Complete 
| 214|0x000000008d600000, 0x000000008d700000, 0x000000008d700000|100%| E|CS|TAMS 0x000000008d600000, 0x000000008d600000| Complete 
| 215|0x000000008d700000, 0x000000008d800000, 0x000000008d800000|100%| E|CS|TAMS 0x000000008d700000, 0x000000008d700000| Complete 
| 216|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%| E|CS|TAMS 0x000000008d800000, 0x000000008d800000| Complete 
| 217|0x000000008d900000, 0x000000008da00000, 0x000000008da00000|100%| E|CS|TAMS 0x000000008d900000, 0x000000008d900000| Complete 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| E|CS|TAMS 0x000000008da00000, 0x000000008da00000| Complete 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| E|CS|TAMS 0x000000008db00000, 0x000000008db00000| Complete 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| E|CS|TAMS 0x000000008dc00000, 0x000000008dc00000| Complete 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| E|CS|TAMS 0x000000008dd00000, 0x000000008dd00000| Complete 
| 222|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%| E|CS|TAMS 0x000000008de00000, 0x000000008de00000| Complete 
| 223|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%| E|CS|TAMS 0x000000008df00000, 0x000000008df00000| Complete 
| 224|0x000000008e000000, 0x000000008e100000, 0x000000008e100000|100%| E|CS|TAMS 0x000000008e000000, 0x000000008e000000| Complete 
| 225|0x000000008e100000, 0x000000008e200000, 0x000000008e200000|100%| E|CS|TAMS 0x000000008e100000, 0x000000008e100000| Complete 
| 226|0x000000008e200000, 0x000000008e300000, 0x000000008e300000|100%| E|CS|TAMS 0x000000008e200000, 0x000000008e200000| Complete 
| 227|0x000000008e300000, 0x000000008e400000, 0x000000008e400000|100%| E|CS|TAMS 0x000000008e300000, 0x000000008e300000| Complete 
| 228|0x000000008e400000, 0x000000008e500000, 0x000000008e500000|100%| E|CS|TAMS 0x000000008e400000, 0x000000008e400000| Complete 
| 229|0x000000008e500000, 0x000000008e600000, 0x000000008e600000|100%| E|CS|TAMS 0x000000008e500000, 0x000000008e500000| Complete 
| 230|0x000000008e600000, 0x000000008e700000, 0x000000008e700000|100%| E|CS|TAMS 0x000000008e600000, 0x000000008e600000| Complete 
| 231|0x000000008e700000, 0x000000008e800000, 0x000000008e800000|100%| E|CS|TAMS 0x000000008e700000, 0x000000008e700000| Complete 
| 232|0x000000008e800000, 0x000000008e900000, 0x000000008e900000|100%| E|CS|TAMS 0x000000008e800000, 0x000000008e800000| Complete 
| 233|0x000000008e900000, 0x000000008ea00000, 0x000000008ea00000|100%| E|CS|TAMS 0x000000008e900000, 0x000000008e900000| Complete 
| 234|0x000000008ea00000, 0x000000008eb00000, 0x000000008eb00000|100%| E|CS|TAMS 0x000000008ea00000, 0x000000008ea00000| Complete 
| 235|0x000000008eb00000, 0x000000008ec00000, 0x000000008ec00000|100%| E|CS|TAMS 0x000000008eb00000, 0x000000008eb00000| Complete 
| 236|0x000000008ec00000, 0x000000008ed00000, 0x000000008ed00000|100%| E|CS|TAMS 0x000000008ec00000, 0x000000008ec00000| Complete 
| 237|0x000000008ed00000, 0x000000008ee00000, 0x000000008ee00000|100%| E|CS|TAMS 0x000000008ed00000, 0x000000008ed00000| Complete 
| 238|0x000000008ee00000, 0x000000008ef00000, 0x000000008ef00000|100%| E|CS|TAMS 0x000000008ee00000, 0x000000008ee00000| Complete 
| 239|0x000000008ef00000, 0x000000008f000000, 0x000000008f000000|100%| E|CS|TAMS 0x000000008ef00000, 0x000000008ef00000| Complete 
| 240|0x000000008f000000, 0x000000008f100000, 0x000000008f100000|100%| E|CS|TAMS 0x000000008f000000, 0x000000008f000000| Complete 
| 241|0x000000008f100000, 0x000000008f200000, 0x000000008f200000|100%| E|CS|TAMS 0x000000008f100000, 0x000000008f100000| Complete 
| 242|0x000000008f200000, 0x000000008f300000, 0x000000008f300000|100%| E|CS|TAMS 0x000000008f200000, 0x000000008f200000| Complete 
| 243|0x000000008f300000, 0x000000008f400000, 0x000000008f400000|100%| E|CS|TAMS 0x000000008f300000, 0x000000008f300000| Complete 
| 244|0x000000008f400000, 0x000000008f500000, 0x000000008f500000|100%| E|CS|TAMS 0x000000008f400000, 0x000000008f400000| Complete 
| 245|0x000000008f500000, 0x000000008f600000, 0x000000008f600000|100%| E|CS|TAMS 0x000000008f500000, 0x000000008f500000| Complete 
| 246|0x000000008f600000, 0x000000008f700000, 0x000000008f700000|100%| E|CS|TAMS 0x000000008f600000, 0x000000008f600000| Complete 
| 247|0x000000008f700000, 0x000000008f800000, 0x000000008f800000|100%| E|CS|TAMS 0x000000008f700000, 0x000000008f700000| Complete 
| 248|0x000000008f800000, 0x000000008f900000, 0x000000008f900000|100%| E|CS|TAMS 0x000000008f800000, 0x000000008f800000| Complete 
| 249|0x000000008f900000, 0x000000008fa00000, 0x000000008fa00000|100%| E|CS|TAMS 0x000000008f900000, 0x000000008f900000| Complete 
| 250|0x000000008fa00000, 0x000000008fb00000, 0x000000008fb00000|100%| E|CS|TAMS 0x000000008fa00000, 0x000000008fa00000| Complete 
| 251|0x000000008fb00000, 0x000000008fc00000, 0x000000008fc00000|100%| E|CS|TAMS 0x000000008fb00000, 0x000000008fb00000| Complete 
| 252|0x000000008fc00000, 0x000000008fd00000, 0x000000008fd00000|100%| E|CS|TAMS 0x000000008fc00000, 0x000000008fc00000| Complete 
| 253|0x000000008fd00000, 0x000000008fe00000, 0x000000008fe00000|100%| E|CS|TAMS 0x000000008fd00000, 0x000000008fd00000| Complete 

Card table byte_map: [0x000001b0793d0000,0x000001b0797d0000] _byte_map_base: 0x000001b078fd0000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001b061e5e3f8, (CMBitMap*) 0x000001b061e5e430
 Prev Bits: [0x000001b079bd0000, 0x000001b07bbd0000)
 Next Bits: [0x000001b07bbd0000, 0x000001b07dbd0000)

Polling page: 0x000001b0607a0000

Metaspace:

Usage:
  Non-class:     41.63 MB capacity,    40.79 MB ( 98%) used,   721.49 KB (  2%) free+waste,   134.81 KB ( <1%) overhead. 
      Class:      6.46 MB capacity,     5.96 MB ( 92%) used,   449.62 KB (  7%) free+waste,    67.38 KB (  1%) overhead. 
       Both:     48.09 MB capacity,    46.75 MB ( 97%) used,     1.14 MB (  2%) free+waste,   202.19 KB ( <1%) overhead. 

Virtual space:
  Non-class space:       42.00 MB reserved,      41.75 MB (>99%) committed 
      Class space:      504.00 MB reserved,       6.50 MB (  1%) committed 
             Both:      546.00 MB reserved,      48.25 MB (  9%) committed 

Chunk freelists:
   Non-Class:  0 bytes
       Class:  1.00 KB
        Both:  1.00 KB

CodeHeap 'non-profiled nmethods': size=120000Kb used=2623Kb max_used=2623Kb free=117376Kb
 bounds [0x000001b071040000, 0x000001b0712d0000, 0x000001b078570000]
CodeHeap 'profiled nmethods': size=120000Kb used=9467Kb max_used=9467Kb free=110532Kb
 bounds [0x000001b069b10000, 0x000001b06a450000, 0x000001b071040000]
CodeHeap 'non-nmethods': size=5760Kb used=2215Kb max_used=2312Kb free=3544Kb
 bounds [0x000001b069570000, 0x000001b0697e0000, 0x000001b069b10000]
 total_blobs=5235 nmethods=4445 adapters=701
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (10 events):
Event: 28.447 Thread 0x000001b001093000 4463       4       java.lang.ClassLoader::compareCerts (201 bytes)
Event: 28.468 Thread 0x000001b001093000 nmethod 4463 0x000001b0712ce390 code [0x000001b0712ce540, 0x000001b0712cebb8]
Event: 28.504 Thread 0x000001b00100d800 4464       1       org.slf4j.impl.StaticLoggerBinder::getLoggerFactory (5 bytes)
Event: 28.504 Thread 0x000001b00100d800 nmethod 4464 0x000001b0712cf090 code [0x000001b0712cf220, 0x000001b0712cf2d8]
Event: 28.566 Thread 0x000001b00100d800 4465       3       org.gradle.internal.instantiation.generator.AsmBackedClassGenerator$ClassBuilderImpl$LocalMethodVisitorScope::<init> (11 bytes)
Event: 28.566 Thread 0x000001b00100d800 nmethod 4465 0x000001b06a443790 code [0x000001b06a443940, 0x000001b06a443b28]
Event: 28.566 Thread 0x000001b00100d800 4466       3       org.gradle.model.internal.asm.ClassVisitorScope::publicMethod (11 bytes)
Event: 28.567 Thread 0x000001b00100d800 nmethod 4466 0x000001b06a443c10 code [0x000001b06a443e00, 0x000001b06a4443f8]
Event: 28.929 Thread 0x000001b001093000 4467       4       java.util.concurrent.locks.AbstractQueuedSynchronizer::release (33 bytes)
Event: 28.931 Thread 0x000001b001093000 nmethod 4467 0x000001b0712cf390 code [0x000001b0712cf520, 0x000001b0712cf5e8]

GC Heap History (10 events):
Event: 14.436 GC heap before
{Heap before GC invocations=2 (full 0):
 garbage-first heap   total 260096K, used 33508K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 28 young (28672K), 13 survivors (13312K)
 Metaspace       used 20806K, capacity 21161K, committed 21248K, reserved 536576K
  class space    used 2414K, capacity 2536K, committed 2560K, reserved 516096K
}
Event: 14.452 GC heap after
{Heap after GC invocations=3 (full 0):
 garbage-first heap   total 260096K, used 20288K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 1 young (1024K), 1 survivors (1024K)
 Metaspace       used 20806K, capacity 21161K, committed 21248K, reserved 536576K
  class space    used 2414K, capacity 2536K, committed 2560K, reserved 516096K
}
Event: 15.887 GC heap before
{Heap before GC invocations=4 (full 0):
 garbage-first heap   total 260096K, used 88896K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 69 young (70656K), 1 survivors (1024K)
 Metaspace       used 25384K, capacity 25979K, committed 26236K, reserved 540672K
  class space    used 3163K, capacity 3386K, committed 3452K, reserved 516096K
}
Event: 15.891 GC heap after
{Heap after GC invocations=5 (full 0):
 garbage-first heap   total 260096K, used 21261K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 25384K, capacity 25979K, committed 26236K, reserved 540672K
  class space    used 3163K, capacity 3386K, committed 3452K, reserved 516096K
}
Event: 23.032 GC heap before
{Heap before GC invocations=5 (full 0):
 garbage-first heap   total 260096K, used 131853K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 110 young (112640K), 2 survivors (2048K)
 Metaspace       used 31847K, capacity 32584K, committed 32892K, reserved 544768K
  class space    used 4111K, capacity 4399K, committed 4476K, reserved 516096K
}
Event: 23.041 GC heap after
{Heap after GC invocations=6 (full 0):
 garbage-first heap   total 260096K, used 24181K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 31847K, capacity 32584K, committed 32892K, reserved 544768K
  class space    used 4111K, capacity 4399K, committed 4476K, reserved 516096K
}
Event: 24.065 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 260096K, used 54901K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 37 young (37888K), 5 survivors (5120K)
 Metaspace       used 34833K, capacity 35833K, committed 35964K, reserved 548864K
  class space    used 4520K, capacity 4893K, committed 4988K, reserved 516096K
}
Event: 24.078 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 260096K, used 26995K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 34833K, capacity 35833K, committed 35964K, reserved 548864K
  class space    used 4520K, capacity 4893K, committed 4988K, reserved 516096K
}
Event: 26.929 GC heap before
{Heap before GC invocations=8 (full 0):
 garbage-first heap   total 260096K, used 139635K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 119 young (121856K), 8 survivors (8192K)
 Metaspace       used 44095K, capacity 45309K, committed 45436K, reserved 557056K
  class space    used 5669K, capacity 6158K, committed 6268K, reserved 516096K
}
Event: 26.947 GC heap after
{Heap after GC invocations=9 (full 0):
 garbage-first heap   total 260096K, used 33163K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 44095K, capacity 45309K, committed 45436K, reserved 557056K
  class space    used 5669K, capacity 6158K, committed 6268K, reserved 516096K
}

Deoptimization events (10 events):
Event: 27.117 Thread 0x000001b0022e4800 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000001b071290098 method=java.lang.PublicMethods$MethodList.filter([Ljava/lang/reflect/Method;Ljava/lang/String;[Ljava/lang/Class;Z)Ljava/lang/PublicMethods$MethodList; @ 21 c2
Event: 27.118 Thread 0x000001b0022e4800 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000001b071290098 method=java.lang.PublicMethods$MethodList.filter([Ljava/lang/reflect/Method;Ljava/lang/String;[Ljava/lang/Class;Z)Ljava/lang/PublicMethods$MethodList; @ 21 c2
Event: 27.118 Thread 0x000001b0022e4800 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000001b071290098 method=java.lang.PublicMethods$MethodList.filter([Ljava/lang/reflect/Method;Ljava/lang/String;[Ljava/lang/Class;Z)Ljava/lang/PublicMethods$MethodList; @ 21 c2
Event: 27.118 Thread 0x000001b0022e4800 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000001b071290098 method=java.lang.PublicMethods$MethodList.filter([Ljava/lang/reflect/Method;Ljava/lang/String;[Ljava/lang/Class;Z)Ljava/lang/PublicMethods$MethodList; @ 21 c2
Event: 27.614 Thread 0x000001b0022e4800 Uncommon trap: reason=intrinsic_or_type_checked_inlining action=make_not_entrant pc=0x000001b0712a866c method=java.util.ArrayList.toArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 36 c2
Event: 27.629 Thread 0x000001b0022e4800 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001b07116b250 method=java.util.Collections$SetFromMap.remove(Ljava/lang/Object;)Z @ 5 c2
Event: 27.647 Thread 0x000001b0022e4800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001b071176630 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 162 c2
Event: 27.676 Thread 0x000001b0022e4800 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001b0712a987c method=java.util.stream.StreamOpFlag.fromCharacteristics(Ljava/util/Spliterator;)I @ 14 c2
Event: 27.762 Thread 0x000001b0022e4800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001b07110c0bc method=java.util.HashMap.hash(Ljava/lang/Object;)I @ 1 c2
Event: 27.763 Thread 0x000001b0022e4800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001b071144cac method=java.util.HashMap.hash(Ljava/lang/Object;)I @ 1 c2

Classes redefined (0 events):
No events

Internal exceptions (10 events):
Event: 28.041 Thread 0x000001b0022e4800 Exception <a 'java/lang/ClassNotFoundException'{0x000000008e015748}: org/gradle/initialization/DefaultSettingsBeanInfo> (0x000000008e015748) thrown at [./open/src/hotspot/share/classfile/systemDictionary.cpp, line 226]
Event: 28.043 Thread 0x000001b0022e4800 Exception <a 'java/lang/ClassNotFoundException'{0x000000008e044f40}: org/gradle/initialization/DefaultSettingsCustomizer> (0x000000008e044f40) thrown at [./open/src/hotspot/share/classfile/systemDictionary.cpp, line 226]
Event: 28.053 Thread 0x000001b0022e4800 Exception <a 'java/lang/ClassNotFoundException'{0x000000008e0b5210}: org/gradle/initialization/DefaultSettings_DecoratedCustomizer> (0x000000008e0b5210) thrown at [./open/src/hotspot/share/classfile/systemDictionary.cpp, line 226]
Event: 28.093 Thread 0x000001b0022e4800 Exception <a 'java/lang/ClassNotFoundException'{0x000000008de1d898}: org/gradle/plugin/management/internal/DefaultPluginManagementSpec_DecoratedBeanInfo> (0x000000008de1d898) thrown at [./open/src/hotspot/share/classfile/systemDictionary.cpp, line 226]
Event: 28.095 Thread 0x000001b0022e4800 Exception <a 'java/lang/ClassNotFoundException'{0x000000008de63400}: org/gradle/plugin/management/internal/DefaultPluginManagementSpecBeanInfo> (0x000000008de63400) thrown at [./open/src/hotspot/share/classfile/systemDictionary.cpp, line 226]
Event: 28.097 Thread 0x000001b0022e4800 Exception <a 'java/lang/ClassNotFoundException'{0x000000008deabca8}: org/gradle/plugin/management/internal/DefaultPluginManagementSpecCustomizer> (0x000000008deabca8) thrown at [./open/src/hotspot/share/classfile/systemDictionary.cpp, line 226]
Event: 28.100 Thread 0x000001b0022e4800 Exception <a 'java/lang/ClassNotFoundException'{0x000000008dd0f2b8}: org/gradle/plugin/management/internal/DefaultPluginManagementSpec_DecoratedCustomizer> (0x000000008dd0f2b8) thrown at [./open/src/hotspot/share/classfile/systemDictionary.cpp, line 226]
Event: 28.135 Thread 0x000001b0022e4800 Exception <a 'java/lang/ClassNotFoundException'{0x000000008dc277d8}: org/gradle/plugin/use/internal/PluginRequestCollector$PluginDependenciesSpecImplBeanInfo> (0x000000008dc277d8) thrown at [./open/src/hotspot/share/classfile/systemDictionary.cpp, line 22
Event: 28.137 Thread 0x000001b0022e4800 Exception <a 'java/lang/ClassNotFoundException'{0x000000008dc63ea0}: org/gradle/plugin/use/internal/PluginRequestCollector$PluginDependenciesSpecImplCustomizer> (0x000000008dc63ea0) thrown at [./open/src/hotspot/share/classfile/systemDictionary.cpp, line 
Event: 28.162 Thread 0x000001b0022e4800 Exception <a 'java/lang/IncompatibleClassChangeError'{0x000000008db2fea8}: Found class java.lang.Object, but interface was expected> (0x000000008db2fea8) thrown at [./open/src/hotspot/share/interpreter/linkResolver.cpp, line 839]

Events (10 events):
Event: 28.539 loading class org/gradle/internal/component/resolution/failure/describer/ResolutionFailureDescriber
Event: 28.539 loading class org/gradle/internal/component/resolution/failure/describer/ResolutionFailureDescriber done
Event: 28.550 loading class org/gradle/internal/component/resolution/failure/exception/VariantSelectionByAttributesException
Event: 28.550 loading class org/gradle/internal/component/resolution/failure/exception/VariantSelectionByAttributesException done
Event: 28.554 loading class org/gradle/internal/logging/text/TreeFormatter
Event: 28.554 loading class org/gradle/internal/logging/text/TreeFormatter done
Event: 28.557 loading class org/gradle/internal/component/resolution/failure/ResolutionCandidateAssessor$AssessedCandidate
Event: 28.557 loading class org/gradle/internal/component/resolution/failure/ResolutionCandidateAssessor$AssessedCandidate done
Event: 28.561 loading class org/gradle/internal/component/resolution/failure/ResolutionCandidateAssessor$AssessedAttribute
Event: 28.561 loading class org/gradle/internal/component/resolution/failure/ResolutionCandidateAssessor$AssessedAttribute done


Dynamic libraries:
0x00007ff659a60000 - 0x00007ff659a70000 	C:\Program Files\Java\jdk-11.0.25\bin\java.exe
0x00007fff7f780000 - 0x00007fff7f9e5000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fff7ebe0000 - 0x00007fff7eca9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fff7ccf0000 - 0x00007fff7d0d8000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fff7d340000 - 0x00007fff7d48b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff6c370000 - 0x00007fff6c389000 	C:\Program Files\Java\jdk-11.0.25\bin\jli.dll
0x00007fff6c250000 - 0x00007fff6c26b000 	C:\Program Files\Java\jdk-11.0.25\bin\VCRUNTIME140.dll
0x00007fff7f490000 - 0x00007fff7f543000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fff7ed90000 - 0x00007fff7ee39000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fff7f3d0000 - 0x00007fff7f476000 	C:\WINDOWS\System32\sechost.dll
0x00007fff7d520000 - 0x00007fff7d635000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fff7f1a0000 - 0x00007fff7f36a000 	C:\WINDOWS\System32\USER32.dll
0x00007fff7d260000 - 0x00007fff7d287000 	C:\WINDOWS\System32\win32u.dll
0x00007fff660e0000 - 0x00007fff6637a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007fff7f710000 - 0x00007fff7f73b000 	C:\WINDOWS\System32\GDI32.dll
0x00007fff7c8d0000 - 0x00007fff7ca07000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fff7d290000 - 0x00007fff7d333000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fff6de40000 - 0x00007fff6de4b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fff7ecd0000 - 0x00007fff7ed00000 	C:\WINDOWS\System32\IMM32.DLL
0x00007fff764a0000 - 0x00007fff764ac000 	C:\Program Files\Java\jdk-11.0.25\bin\vcruntime140_1.dll
0x00007fff51890000 - 0x00007fff5191e000 	C:\Program Files\Java\jdk-11.0.25\bin\msvcp140.dll
0x00007fff0ec90000 - 0x00007fff0f7f3000 	C:\Program Files\Java\jdk-11.0.25\bin\server\jvm.dll
0x00007fff7dbf0000 - 0x00007fff7dbf8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007fff58c00000 - 0x00007fff58c0a000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007fff666f0000 - 0x00007fff66725000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fff7eeb0000 - 0x00007fff7ef24000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fff7b7b0000 - 0x00007fff7b7cb000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fff76440000 - 0x00007fff76450000 	C:\Program Files\Java\jdk-11.0.25\bin\verify.dll
0x00007fff76bf0000 - 0x00007fff76e31000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fff7d860000 - 0x00007fff7dbe5000 	C:\WINDOWS\System32\combase.dll
0x00007fff7f0b0000 - 0x00007fff7f191000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fff61080000 - 0x00007fff610b9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fff7ca10000 - 0x00007fff7caa9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fff67cc0000 - 0x00007fff67ce8000 	C:\Program Files\Java\jdk-11.0.25\bin\java.dll
0x00007fff6f470000 - 0x00007fff6f47a000 	C:\Program Files\Java\jdk-11.0.25\bin\jimage.dll
0x00007fff76450000 - 0x00007fff7645e000 	C:\Program Files\Java\jdk-11.0.25\bin\instrument.dll
0x00007fff6bdd0000 - 0x00007fff6bde7000 	C:\Program Files\Java\jdk-11.0.25\bin\zip.dll
0x00007fff7e140000 - 0x00007fff7e882000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fff7d0e0000 - 0x00007fff7d254000 	C:\WINDOWS\System32\wintypes.dll
0x00007fff7a590000 - 0x00007fff7ade8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fff7f580000 - 0x00007fff7f671000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fff7d640000 - 0x00007fff7d6aa000 	C:\WINDOWS\System32\shlwapi.dll
0x00007fff7c7e0000 - 0x00007fff7c80f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff6bbd0000 - 0x00007fff6bbe9000 	C:\Program Files\Java\jdk-11.0.25\bin\net.dll
0x00007fff76ff0000 - 0x00007fff7710e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007fff7bd20000 - 0x00007fff7bd8a000 	C:\WINDOWS\system32\mswsock.dll
0x00007fff67d80000 - 0x00007fff67d92000 	C:\Program Files\Java\jdk-11.0.25\bin\nio.dll
0x00007fff66b80000 - 0x00007fff66ba7000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00000000718d0000 - 0x0000000071943000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007fff6c200000 - 0x00007fff6c209000 	C:\Program Files\Java\jdk-11.0.25\bin\management.dll
0x00007fff6bc90000 - 0x00007fff6bc9b000 	C:\Program Files\Java\jdk-11.0.25\bin\management_ext.dll
0x00007fff7bfd0000 - 0x00007fff7bfeb000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fff7b710000 - 0x00007fff7b74a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fff7bdc0000 - 0x00007fff7bdeb000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fff7c7b0000 - 0x00007fff7c7d6000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007fff7bff0000 - 0x00007fff7bffc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fff7b150000 - 0x00007fff7b183000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fff7f480000 - 0x00007fff7f48a000 	C:\WINDOWS\System32\NSI.dll
0x00007fff6dd30000 - 0x00007fff6dd4f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007fff6dd00000 - 0x00007fff6dd25000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007fff7b1e0000 - 0x00007fff7b307000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x0000000071850000 - 0x00000000718c3000 	C:\Users\<USER>\AppData\Local\Temp\native-platform7139610037139523201dir\gradle-fileevents.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-11.0.25\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;C:\Program Files\Java\jdk-11.0.25\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform7139610037139523201dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=512m -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 528482304                                 {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxMetaspaceSize                         = 536870912                                 {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5836300                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122910970                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122910970                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-11.0.25
PATH=C:\Gradle\gradle-8.13\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\Java\jdk-11.0.25\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\SqlCmd\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64\compiler;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Intel\WiFi\bin\;C:\Program Files\Common Files\Intel\WirelessCommon\;C:\ProgramData\chocolatey\bin;C:\Python312\Scripts;C:\Python312;C:\Program Files\dotnet\;C:\xampp\php\php.exe;C:\php;C:\tools\php84;C:\xampp\mysql\bin;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\ncat;C:\curl;C:\Users\<USER>\AppData\Roaming\npm\node_modules;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Program Files\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundatio;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\Gradle\gradle-8.13\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\Scripts;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files (x86)\Nmap;C:\xampp\mysql\b;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\MongoDB\Server\7.0\bin;C:\Program Files\MongoDB\Server\8.0\bin;;C:\PostgreSQL\16\bin;C:\Program Files\SqlCmd\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\Com
USERNAME=Sheldon
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 10, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 11 , 64 bit Build 26100 (10.0.26100.4202)

CPU:total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 142 stepping 10 microcode 0xf6, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, rtm, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, fma

Memory: 4k page, system-wide physical 16222M (1177M free)
TotalPageFile size 21072M (AvailPageFile size 39M)
current process WorkingSet (physical memory assigned to process): 401M, peak: 405M
current process commit charge ("private bytes"): 513M, peak: 521M

vm_info: Java HotSpot(TM) 64-Bit Server VM (11.0.25+9-LTS-256) for windows-amd64 JRE (11.0.25+9-LTS-256), built on Sep 30 2024 06:30:20 by "mach5one" with MS VC++ 17.6 (VS2022)

END.
