#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1080976 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (./open/src/hotspot/share/memory/arena.cpp:197), pid=25204, tid=14584
#
# JRE version: Java(TM) SE Runtime Environment 18.9 (11.0.25+9) (build 11.0.25+9-LTS-256)
# Java VM: Java HotSpot(TM) 64-Bit Server VM 18.9 (11.0.25+9-LTS-256, mixed mode, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=512m -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: Intel(R) Core(TM) i7-8650U CPU @ 1.90GHz, 8 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Tue Jul  1 12:34:16 2025 E. Africa Standard Time elapsed time: 61.888602 seconds (0d 0h 1m 1s)

---------------  T H R E A D  ---------------

Current thread (0x0000014f9452d000):  JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=14584, stack(0x0000002e6bc00000,0x0000002e6bd00000)]


Current CompileTask:
C2:  61888 16827       4       org.jetbrains.kotlin.types.TypeSubstitutor::unsafeSubstitute (793 bytes)

Stack: [0x0000002e6bc00000,0x0000002e6bd00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x65824a]
V  [jvm.dll+0x79ae8f]
V  [jvm.dll+0x79c559]
V  [jvm.dll+0x79cc03]
V  [jvm.dll+0x255785]
V  [jvm.dll+0xb312c]
V  [jvm.dll+0xb373c]
V  [jvm.dll+0x2c6b92]
V  [jvm.dll+0x567d87]
V  [jvm.dll+0x217e21]
V  [jvm.dll+0x211663]
V  [jvm.dll+0x20dfd4]
V  [jvm.dll+0x18b381]
V  [jvm.dll+0x21e774]
V  [jvm.dll+0x21ca9c]
V  [jvm.dll+0x75ed51]
V  [jvm.dll+0x757674]
V  [jvm.dll+0x6570f5]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000014f987e2f90, length=68, elements={
0x0000014ff0e8a000, 0x0000014f8f837000, 0x0000014f8f840800, 0x0000014f8f854000,
0x0000014f8f855000, 0x0000014f8f858000, 0x0000014f8f859000, 0x0000014f8f85a000,
0x0000014f8f8a1800, 0x0000014f8fa11000, 0x0000014f92008000, 0x0000014f8ff85800,
0x0000014f8ff80800, 0x0000014f91082800, 0x0000014f8ff65000, 0x0000014f8fb41000,
0x0000014f90f93800, 0x0000014f90bc1000, 0x0000014f9022b000, 0x0000014f9022b800,
0x0000014f90b62000, 0x0000014f90b5f800, 0x0000014f90b62800, 0x0000014f90b60000,
0x0000014f90b63800, 0x0000014f90b61000, 0x0000014f90b64800, 0x0000014f90b65000,
0x0000014f908a6800, 0x0000014f908a7800, 0x0000014f908a9000, 0x0000014f908aa800,
0x0000014f908ac000, 0x0000014f908a9800, 0x0000014f908ad000, 0x0000014f908af800,
0x0000014f908ae000, 0x0000014f908ae800, 0x0000014f908b2000, 0x0000014f908b1000,
0x0000014f908b2800, 0x0000014f908b0800, 0x0000014f908b3800, 0x0000014f908b4800,
0x0000014f908b5000, 0x0000014f95722000, 0x0000014f95723000, 0x0000014f95721800,
0x0000014f95724000, 0x0000014f95727000, 0x0000014f95725800, 0x0000014f95724800,
0x0000014f95728000, 0x0000014f95728800, 0x0000014f95726000, 0x0000014f9572b000,
0x0000014f9572a800, 0x0000014f9572c000, 0x0000014f95729800, 0x0000014f9572c800,
0x0000014f95730000, 0x0000014f9572e800, 0x0000014f9572f000, 0x0000014f9572d800,
0x0000014f9ae51000, 0x0000014f9ae54800, 0x0000014f9452b800, 0x0000014f9452d000
}

Java Threads: ( => current thread )
  0x0000014ff0e8a000 JavaThread "main" [_thread_blocked, id=14904, stack(0x0000002e66300000,0x0000002e66400000)]
  0x0000014f8f837000 JavaThread "Reference Handler" daemon [_thread_blocked, id=14080, stack(0x0000002e66a00000,0x0000002e66b00000)]
  0x0000014f8f840800 JavaThread "Finalizer" daemon [_thread_blocked, id=7612, stack(0x0000002e66b00000,0x0000002e66c00000)]
  0x0000014f8f854000 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=13872, stack(0x0000002e66c00000,0x0000002e66d00000)]
  0x0000014f8f855000 JavaThread "Attach Listener" daemon [_thread_blocked, id=6172, stack(0x0000002e66d00000,0x0000002e66e00000)]
  0x0000014f8f858000 JavaThread "Service Thread" daemon [_thread_blocked, id=25584, stack(0x0000002e66e00000,0x0000002e66f00000)]
  0x0000014f8f859000 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=12108, stack(0x0000002e66f00000,0x0000002e67000000)]
  0x0000014f8f85a000 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=5912, stack(0x0000002e67000000,0x0000002e67100000)]
  0x0000014f8f8a1800 JavaThread "Sweeper thread" daemon [_thread_blocked, id=24192, stack(0x0000002e67100000,0x0000002e67200000)]
  0x0000014f8fa11000 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=24592, stack(0x0000002e67200000,0x0000002e67300000)]
  0x0000014f92008000 JavaThread "Daemon health stats" [_thread_blocked, id=7644, stack(0x0000002e67300000,0x0000002e67400000)]
  0x0000014f8ff85800 JavaThread "Incoming local TCP Connector on port 37286" [_thread_in_native, id=19188, stack(0x0000002e67500000,0x0000002e67600000)]
  0x0000014f8ff80800 JavaThread "Daemon periodic checks" [_thread_blocked, id=9280, stack(0x0000002e67c00000,0x0000002e67d00000)]
  0x0000014f91082800 JavaThread "Daemon" [_thread_blocked, id=10996, stack(0x0000002e67d00000,0x0000002e67e00000)]
  0x0000014f8ff65000 JavaThread "Handler for socket connection from /127.0.0.1:37286 to /127.0.0.1:37287" [_thread_in_native, id=17472, stack(0x0000002e67e00000,0x0000002e67f00000)]
  0x0000014f8fb41000 JavaThread "Cancel handler" [_thread_blocked, id=26144, stack(0x0000002e67b00000,0x0000002e67c00000)]
  0x0000014f90f93800 JavaThread "Daemon worker" [_thread_in_native, id=25628, stack(0x0000002e67f00000,0x0000002e68000000)]
  0x0000014f90bc1000 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:37286 to /127.0.0.1:37287" [_thread_blocked, id=19836, stack(0x0000002e68000000,0x0000002e68100000)]
  0x0000014f9022b000 JavaThread "Stdin handler" [_thread_blocked, id=24432, stack(0x0000002e68100000,0x0000002e68200000)]
  0x0000014f9022b800 JavaThread "Daemon client event forwarder" [_thread_blocked, id=23904, stack(0x0000002e68200000,0x0000002e68300000)]
  0x0000014f90b62000 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=12632, stack(0x0000002e68700000,0x0000002e68800000)]
  0x0000014f90b5f800 JavaThread "File lock request listener" [_thread_in_native, id=20964, stack(0x0000002e68800000,0x0000002e68900000)]
  0x0000014f90b62800 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)" [_thread_blocked, id=25864, stack(0x0000002e68900000,0x0000002e68a00000)]
  0x0000014f90b60000 JavaThread "Problems report writer" [_thread_blocked, id=18552, stack(0x0000002e68b00000,0x0000002e68c00000)]
  0x0000014f90b63800 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\android\.gradle\8.12\fileHashes)" [_thread_blocked, id=26496, stack(0x0000002e68c00000,0x0000002e68d00000)]
  0x0000014f90b61000 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=6268, stack(0x0000002e68d00000,0x0000002e68e00000)]
  0x0000014f90b64800 JavaThread "File watcher server" daemon [_thread_in_native, id=21552, stack(0x0000002e68e00000,0x0000002e68f00000)]
  0x0000014f90b65000 JavaThread "File watcher consumer" daemon [_thread_blocked, id=7076, stack(0x0000002e68f00000,0x0000002e69000000)]
  0x0000014f908a6800 JavaThread "jar transforms" [_thread_blocked, id=24872, stack(0x0000002e69000000,0x0000002e69100000)]
  0x0000014f908a7800 JavaThread "jar transforms Thread 2" [_thread_blocked, id=18828, stack(0x0000002e69100000,0x0000002e69200000)]
  0x0000014f908a9000 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\android\.gradle\8.12\checksums)" [_thread_blocked, id=5000, stack(0x0000002e69200000,0x0000002e69300000)]
  0x0000014f908aa800 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)" [_thread_blocked, id=20980, stack(0x0000002e69300000,0x0000002e69400000)]
  0x0000014f908ac000 JavaThread "jar transforms Thread 3" [_thread_blocked, id=1040, stack(0x0000002e69400000,0x0000002e69500000)]
  0x0000014f908a9800 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.12\md-rule)" [_thread_blocked, id=24608, stack(0x0000002e69500000,0x0000002e69600000)]
  0x0000014f908ad000 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.12\md-supplier)" [_thread_blocked, id=26100, stack(0x0000002e69600000,0x0000002e69700000)]
  0x0000014f908af800 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)" [_thread_blocked, id=25824, stack(0x0000002e69700000,0x0000002e69800000)]
  0x0000014f908ae000 JavaThread "Unconstrained build operations" [_thread_blocked, id=11096, stack(0x0000002e69800000,0x0000002e69900000)]
  0x0000014f908ae800 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=25884, stack(0x0000002e69900000,0x0000002e69a00000)]
  0x0000014f908b2000 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=20564, stack(0x0000002e69a00000,0x0000002e69b00000)]
  0x0000014f908b1000 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=21040, stack(0x0000002e69b00000,0x0000002e69c00000)]
  0x0000014f908b2800 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=24248, stack(0x0000002e69c00000,0x0000002e69d00000)]
  0x0000014f908b0800 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=7252, stack(0x0000002e69d00000,0x0000002e69e00000)]
  0x0000014f908b3800 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=16240, stack(0x0000002e69e00000,0x0000002e69f00000)]
  0x0000014f908b4800 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=16980, stack(0x0000002e69f00000,0x0000002e6a000000)]
  0x0000014f908b5000 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=16756, stack(0x0000002e6a000000,0x0000002e6a100000)]
  0x0000014f95722000 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=22212, stack(0x0000002e6a100000,0x0000002e6a200000)]
  0x0000014f95723000 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=20116, stack(0x0000002e6a200000,0x0000002e6a300000)]
  0x0000014f95721800 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=17956, stack(0x0000002e6a300000,0x0000002e6a400000)]
  0x0000014f95724000 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=12596, stack(0x0000002e6a400000,0x0000002e6a500000)]
  0x0000014f95727000 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=18816, stack(0x0000002e6a500000,0x0000002e6a600000)]
  0x0000014f95725800 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=24572, stack(0x0000002e6a600000,0x0000002e6a700000)]
  0x0000014f95724800 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=22092, stack(0x0000002e6a700000,0x0000002e6a800000)]
  0x0000014f95728000 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=1568, stack(0x0000002e6a800000,0x0000002e6a900000)]
  0x0000014f95728800 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=25112, stack(0x0000002e6a900000,0x0000002e6aa00000)]
  0x0000014f95726000 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=17208, stack(0x0000002e6aa00000,0x0000002e6ab00000)]
  0x0000014f9572b000 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=25816, stack(0x0000002e6ab00000,0x0000002e6ac00000)]
  0x0000014f9572a800 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=21900, stack(0x0000002e6ac00000,0x0000002e6ad00000)]
  0x0000014f9572c000 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=12588, stack(0x0000002e6ad00000,0x0000002e6ae00000)]
  0x0000014f95729800 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=10264, stack(0x0000002e6ae00000,0x0000002e6af00000)]
  0x0000014f9572c800 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=5580, stack(0x0000002e6af00000,0x0000002e6b000000)]
  0x0000014f95730000 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=26552, stack(0x0000002e6b000000,0x0000002e6b100000)]
  0x0000014f9572e800 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=2568, stack(0x0000002e6b100000,0x0000002e6b200000)]
  0x0000014f9572f000 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=1796, stack(0x0000002e6b200000,0x0000002e6b300000)]
  0x0000014f9572d800 JavaThread "Kotlin DSL Writer" [_thread_blocked, id=15140, stack(0x0000002e68a00000,0x0000002e68b00000)]
  0x0000014f9ae51000 JavaThread "build event listener" [_thread_blocked, id=6556, stack(0x0000002e6ba00000,0x0000002e6bb00000)]
  0x0000014f9ae54800 JavaThread "Memory manager" [_thread_blocked, id=25804, stack(0x0000002e6bb00000,0x0000002e6bc00000)]
  0x0000014f9452b800 JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=15056, stack(0x0000002e66000000,0x0000002e66100000)]
=>0x0000014f9452d000 JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=14584, stack(0x0000002e6bc00000,0x0000002e6bd00000)]

Other Threads:
  0x0000014f8f830000 VMThread "VM Thread" [stack: 0x0000002e66900000,0x0000002e66a00000] [id=24064]
  0x0000014f8fddb800 WatcherThread [stack: 0x0000002e67400000,0x0000002e67500000] [id=24104]
  0x0000014ff0ea0800 GCTaskThread "GC Thread#0" [stack: 0x0000002e66400000,0x0000002e66500000] [id=24988]
  0x0000014f90453000 GCTaskThread "GC Thread#1" [stack: 0x0000002e67600000,0x0000002e67700000] [id=15204]
  0x0000014f900e6800 GCTaskThread "GC Thread#2" [stack: 0x0000002e67700000,0x0000002e67800000] [id=15752]
  0x0000014f900e4800 GCTaskThread "GC Thread#3" [stack: 0x0000002e67800000,0x0000002e67900000] [id=25644]
  0x0000014f90260000 GCTaskThread "GC Thread#4" [stack: 0x0000002e67900000,0x0000002e67a00000] [id=9456]
  0x0000014f90260800 GCTaskThread "GC Thread#5" [stack: 0x0000002e67a00000,0x0000002e67b00000] [id=24920]
  0x0000014f91740000 GCTaskThread "GC Thread#6" [stack: 0x0000002e68300000,0x0000002e68400000] [id=26388]
  0x0000014f90483800 GCTaskThread "GC Thread#7" [stack: 0x0000002e68400000,0x0000002e68500000] [id=24880]
  0x0000014ff0ed5000 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000002e66500000,0x0000002e66600000] [id=23856]
  0x0000014ff0ed6800 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000002e66600000,0x0000002e66700000] [id=25536]
  0x0000014f90a5c000 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000002e68600000,0x0000002e68700000] [id=13412]
  0x0000014fffde2800 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000002e66700000,0x0000002e66800000] [id=19256]
  0x0000014f9ac94800 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000002e68500000,0x0000002e68600000] [id=25180]
  0x0000014f9ac98000 ConcurrentGCThread "G1 Refine#2" [stack: 0x0000002e6b300000,0x0000002e6b400000] [id=14868]
  0x0000014f9ac9b800 ConcurrentGCThread "G1 Refine#3" [stack: 0x0000002e6b400000,0x0000002e6b500000] [id=22912]
  0x0000014f9ac98800 ConcurrentGCThread "G1 Refine#4" [stack: 0x0000002e6b500000,0x0000002e6b600000] [id=2072]
  0x0000014f9ac9d000 ConcurrentGCThread "G1 Refine#5" [stack: 0x0000002e6b600000,0x0000002e6b700000] [id=24824]
  0x0000014f9ac9b000 ConcurrentGCThread "G1 Refine#6" [stack: 0x0000002e6b700000,0x0000002e6b800000] [id=9956]
  0x0000014fffde3800 ConcurrentGCThread "G1 Young RemSet Sampling" [stack: 0x0000002e66800000,0x0000002e66900000] [id=19376]

Threads with active compile tasks:
C2 CompilerThread0    61967 16786       4       org.jetbrains.kotlin.load.java.structure.impl.classFiles.BinaryJavaMethodBase$Companion::create (572 bytes)
C1 CompilerThread0    61967 17012       3       org.jetbrains.kotlin.serialization.deserialization.descriptors.DeserializedTypeParameterDescriptor::<init> (106 bytes)
C2 CompilerThread1    61967 16643   !   4       org.jetbrains.kotlin.metadata.ProtoBuf$Class::<init> (2890 bytes)
C2 CompilerThread2    61967 16827       4       org.jetbrains.kotlin.types.TypeSubstitutor::unsafeSubstitute (793 bytes)

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 528482304 Address: 0x0000000100000000

Heap:
 garbage-first heap   total 260096K, used 153028K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 26 young (26624K), 2 survivors (2048K)
 Metaspace       used 147020K, capacity 151438K, committed 151688K, reserved 649216K
  class space    used 18305K, capacity 20030K, committed 20076K, reserved 516096K
Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, A=archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%| O|  |TAMS 0x0000000080100000, 0x0000000080000000| Untracked 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%| O|  |TAMS 0x0000000080200000, 0x0000000080100000| Untracked 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HS|  |TAMS 0x0000000080300000, 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080400000, 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%|HC|  |TAMS 0x0000000080500000, 0x0000000080400000| Complete 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%|HS|  |TAMS 0x0000000080600000, 0x0000000080500000| Complete 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080700000, 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080800000, 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000, 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000, 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000, 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000, 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080d00000, 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000, 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080effff8, 0x0000000080f00000| 99%| O|  |TAMS 0x0000000080effff8, 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000, 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000, 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000, 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081300000, 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%|HS|  |TAMS 0x0000000081400000, 0x0000000081300000| Complete 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081500000, 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081600000, 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081700000, 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081800000, 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081900000, 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081a00000, 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081b00000, 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081c00000, 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081d00000, 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081e00000, 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081f00000, 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000082000000, 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%|HS|  |TAMS 0x0000000082100000, 0x0000000082000000| Complete 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%|HC|  |TAMS 0x0000000082200000, 0x0000000082100000| Complete 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%|HC|  |TAMS 0x0000000082300000, 0x0000000082200000| Complete 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%|HC|  |TAMS 0x0000000082400000, 0x0000000082300000| Complete 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%|HC|  |TAMS 0x0000000082500000, 0x0000000082400000| Complete 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%|HC|  |TAMS 0x0000000082600000, 0x0000000082500000| Complete 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%|HS|  |TAMS 0x0000000082700000, 0x0000000082600000| Complete 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%|HC|  |TAMS 0x0000000082800000, 0x0000000082700000| Complete 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%|HS|  |TAMS 0x0000000082900000, 0x0000000082800000| Complete 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%|HC|  |TAMS 0x0000000082a00000, 0x0000000082900000| Complete 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%|HC|  |TAMS 0x0000000082b00000, 0x0000000082a00000| Complete 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082c00000, 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082d00000, 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%| O|  |TAMS 0x0000000082e00000, 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%| O|  |TAMS 0x0000000082f00000, 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%| O|  |TAMS 0x0000000083000000, 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%| O|  |TAMS 0x0000000083100000, 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%| O|  |TAMS 0x0000000083200000, 0x0000000083100000| Untracked 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%| O|  |TAMS 0x0000000083300000, 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%| O|  |TAMS 0x0000000083400000, 0x0000000083300000| Untracked 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%| O|  |TAMS 0x0000000083500000, 0x0000000083400000| Untracked 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%| O|  |TAMS 0x0000000083600000, 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%| O|  |TAMS 0x0000000083700000, 0x0000000083600000| Untracked 
|  55|0x0000000083700000, 0x0000000083700000, 0x0000000083800000|  0%| F|  |TAMS 0x0000000083700000, 0x0000000083700000| Untracked 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%| O|  |TAMS 0x0000000083900000, 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%| O|  |TAMS 0x0000000083a00000, 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%| O|  |TAMS 0x0000000083b00000, 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%| O|  |TAMS 0x0000000083c00000, 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%| O|  |TAMS 0x0000000083d00000, 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083e00000, 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%| O|  |TAMS 0x0000000083f00000, 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000084000000, 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%| O|  |TAMS 0x0000000084100000, 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084200000, 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084300000, 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084400000, 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084500000, 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084600000, 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084700000, 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084800000, 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084900000, 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%| O|  |TAMS 0x0000000084a00000, 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084b00000, 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084c00000, 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084d00000, 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084e00000, 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084f00000, 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000085000000, 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085100000, 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085200000, 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085300000, 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085400000, 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085500000, 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085600000, 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085700000, 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085800000, 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085900000, 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085900000, 0x0000000085a00000|  0%| F|  |TAMS 0x0000000085900000, 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085b00000, 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%|HS|  |TAMS 0x0000000085c00000, 0x0000000085b00000| Complete 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%|HC|  |TAMS 0x0000000085d00000, 0x0000000085c00000| Complete 
|  93|0x0000000085d00000, 0x0000000085d00000, 0x0000000085e00000|  0%| F|  |TAMS 0x0000000085d00000, 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085e00000, 0x0000000085f00000|  0%| F|  |TAMS 0x0000000085e00000, 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000085f00000, 0x0000000086000000|  0%| F|  |TAMS 0x0000000085f00000, 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086100000, 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%|HS|  |TAMS 0x0000000086200000, 0x0000000086100000| Complete 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%|HC|  |TAMS 0x0000000086300000, 0x0000000086200000| Complete 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%|HC|  |TAMS 0x0000000086400000, 0x0000000086300000| Complete 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086500000, 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086600000, 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086600000, 0x0000000086700000|  0%| F|  |TAMS 0x0000000086600000, 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086800000, 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086900000, 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086a00000, 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086b00000, 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086c00000, 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086c00000, 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086d00000, 0x0000000086d00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086e00000, 0x0000000086f00000|  0%| F|  |TAMS 0x0000000086e00000, 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000087000000, 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087100000, 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087200000, 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087300000, 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| O|  |TAMS 0x0000000087400000, 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087400000, 0x0000000087500000|  0%| F|  |TAMS 0x0000000087400000, 0x0000000087400000| Untracked 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087500000, 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087600000, 0x0000000087700000|  0%| F|  |TAMS 0x0000000087600000, 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087700000, 0x0000000087800000|  0%| F|  |TAMS 0x0000000087700000, 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087800000, 0x0000000087900000|  0%| F|  |TAMS 0x0000000087800000, 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087900000, 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087a00000, 0x0000000087b00000|  0%| F|  |TAMS 0x0000000087a00000, 0x0000000087a00000| Untracked 
| 123|0x0000000087b00000, 0x0000000087b00000, 0x0000000087c00000|  0%| F|  |TAMS 0x0000000087b00000, 0x0000000087b00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| O|  |TAMS 0x0000000087d00000, 0x0000000087c00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| O|  |TAMS 0x0000000087e00000, 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087f00000, 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000087f00000, 0x0000000088000000|  0%| F|  |TAMS 0x0000000087f00000, 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088000000, 0x0000000088100000|  0%| F|  |TAMS 0x0000000088000000, 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088100000, 0x0000000088200000|  0%| F|  |TAMS 0x0000000088100000, 0x0000000088100000| Untracked 
| 130|0x0000000088200000, 0x0000000088200000, 0x0000000088300000|  0%| F|  |TAMS 0x0000000088200000, 0x0000000088200000| Untracked 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| O|  |TAMS 0x0000000088400000, 0x0000000088300000| Untracked 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%| O|  |TAMS 0x0000000088400000, 0x0000000088400000| Untracked 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%| O|  |TAMS 0x0000000088500000, 0x0000000088500000| Untracked 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%| O|  |TAMS 0x0000000088600000, 0x0000000088600000| Untracked 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%| O|  |TAMS 0x0000000088700000, 0x0000000088700000| Untracked 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%| O|  |TAMS 0x0000000088800000, 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| O|  |TAMS 0x0000000088900000, 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088a00000, 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|  |TAMS 0x0000000088b00000, 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| O|  |TAMS 0x0000000088c00000, 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088d90c00, 0x0000000088e00000| 56%| O|  |TAMS 0x0000000088d00000, 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088e00000, 0x0000000088f00000|  0%| F|  |TAMS 0x0000000088e00000, 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000088f00000, 0x0000000089000000|  0%| F|  |TAMS 0x0000000088f00000, 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089000000, 0x0000000089100000|  0%| F|  |TAMS 0x0000000089000000, 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089100000, 0x0000000089200000|  0%| F|  |TAMS 0x0000000089100000, 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089200000, 0x0000000089300000|  0%| F|  |TAMS 0x0000000089200000, 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089300000, 0x0000000089400000|  0%| F|  |TAMS 0x0000000089300000, 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089400000, 0x0000000089500000|  0%| F|  |TAMS 0x0000000089400000, 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089500000, 0x0000000089600000|  0%| F|  |TAMS 0x0000000089500000, 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x0000000089600000, 0x0000000089700000|  0%| F|  |TAMS 0x0000000089600000, 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089700000, 0x0000000089800000|  0%| F|  |TAMS 0x0000000089700000, 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089800000, 0x0000000089900000|  0%| F|  |TAMS 0x0000000089800000, 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089900000, 0x0000000089a00000|  0%| F|  |TAMS 0x0000000089900000, 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089a00000, 0x0000000089b00000|  0%| F|  |TAMS 0x0000000089a00000, 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089b00000, 0x0000000089c00000|  0%| F|  |TAMS 0x0000000089b00000, 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089c00000, 0x0000000089d00000|  0%| F|  |TAMS 0x0000000089c00000, 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089d00000, 0x0000000089e00000|  0%| F|  |TAMS 0x0000000089d00000, 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089e00000, 0x0000000089f00000|  0%| F|  |TAMS 0x0000000089e00000, 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x0000000089f00000, 0x000000008a000000|  0%| F|  |TAMS 0x0000000089f00000, 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a000000, 0x000000008a100000|  0%| F|  |TAMS 0x000000008a000000, 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a100000, 0x000000008a200000|  0%| F|  |TAMS 0x000000008a100000, 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a200000, 0x000000008a300000|  0%| F|  |TAMS 0x000000008a200000, 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a300000, 0x000000008a400000|  0%| F|  |TAMS 0x000000008a300000, 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a400000, 0x000000008a500000|  0%| F|  |TAMS 0x000000008a400000, 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a500000, 0x000000008a600000|  0%| F|  |TAMS 0x000000008a500000, 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a600000, 0x000000008a700000|  0%| F|  |TAMS 0x000000008a600000, 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a700000, 0x000000008a800000|  0%| F|  |TAMS 0x000000008a700000, 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a800000, 0x000000008a900000|  0%| F|  |TAMS 0x000000008a800000, 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008a900000, 0x000000008aa00000|  0%| F|  |TAMS 0x000000008a900000, 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008aa00000, 0x000000008ab00000|  0%| F|  |TAMS 0x000000008aa00000, 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ab00000, 0x000000008ac00000|  0%| F|  |TAMS 0x000000008ab00000, 0x000000008ab00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ac00000, 0x000000008ad00000|  0%| F|  |TAMS 0x000000008ac00000, 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ad00000, 0x000000008ae00000|  0%| F|  |TAMS 0x000000008ad00000, 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008ae00000, 0x000000008af00000|  0%| F|  |TAMS 0x000000008ae00000, 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008af00000, 0x000000008b000000|  0%| F|  |TAMS 0x000000008af00000, 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b000000, 0x000000008b100000|  0%| F|  |TAMS 0x000000008b000000, 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b100000, 0x000000008b200000|  0%| F|  |TAMS 0x000000008b100000, 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b200000, 0x000000008b300000|  0%| F|  |TAMS 0x000000008b200000, 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b300000, 0x000000008b400000|  0%| F|  |TAMS 0x000000008b300000, 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b400000, 0x000000008b500000|  0%| F|  |TAMS 0x000000008b400000, 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b500000, 0x000000008b600000|  0%| F|  |TAMS 0x000000008b500000, 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b600000, 0x000000008b700000|  0%| F|  |TAMS 0x000000008b600000, 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b700000, 0x000000008b800000|  0%| F|  |TAMS 0x000000008b700000, 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b800000, 0x000000008b900000|  0%| F|  |TAMS 0x000000008b800000, 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008b900000, 0x000000008ba00000|  0%| F|  |TAMS 0x000000008b900000, 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008ba00000, 0x000000008bb00000|  0%| F|  |TAMS 0x000000008ba00000, 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bb00000, 0x000000008bc00000|  0%| F|  |TAMS 0x000000008bb00000, 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bc00000, 0x000000008bd00000|  0%| F|  |TAMS 0x000000008bc00000, 0x000000008bc00000| Untracked 
| 189|0x000000008bd00000, 0x000000008bd00000, 0x000000008be00000|  0%| F|  |TAMS 0x000000008bd00000, 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008be00000, 0x000000008bf00000|  0%| F|  |TAMS 0x000000008be00000, 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008bf00000, 0x000000008c000000|  0%| F|  |TAMS 0x000000008bf00000, 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c000000, 0x000000008c100000|  0%| F|  |TAMS 0x000000008c000000, 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c100000, 0x000000008c200000|  0%| F|  |TAMS 0x000000008c100000, 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c200000, 0x000000008c300000|  0%| F|  |TAMS 0x000000008c200000, 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c300000, 0x000000008c400000|  0%| F|  |TAMS 0x000000008c300000, 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c400000, 0x000000008c500000|  0%| F|  |TAMS 0x000000008c400000, 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c500000, 0x000000008c600000|  0%| F|  |TAMS 0x000000008c500000, 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c600000, 0x000000008c700000|  0%| F|  |TAMS 0x000000008c600000, 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c700000, 0x000000008c800000|  0%| F|  |TAMS 0x000000008c700000, 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c800000, 0x000000008c900000|  0%| F|  |TAMS 0x000000008c800000, 0x000000008c800000| Untracked 
| 201|0x000000008c900000, 0x000000008c900000, 0x000000008ca00000|  0%| F|  |TAMS 0x000000008c900000, 0x000000008c900000| Untracked 
| 202|0x000000008ca00000, 0x000000008ca00000, 0x000000008cb00000|  0%| F|  |TAMS 0x000000008ca00000, 0x000000008ca00000| Untracked 
| 203|0x000000008cb00000, 0x000000008cb00000, 0x000000008cc00000|  0%| F|  |TAMS 0x000000008cb00000, 0x000000008cb00000| Untracked 
| 204|0x000000008cc00000, 0x000000008cc00000, 0x000000008cd00000|  0%| F|  |TAMS 0x000000008cc00000, 0x000000008cc00000| Untracked 
| 205|0x000000008cd00000, 0x000000008cd00000, 0x000000008ce00000|  0%| F|  |TAMS 0x000000008cd00000, 0x000000008cd00000| Untracked 
| 206|0x000000008ce00000, 0x000000008ce00000, 0x000000008cf00000|  0%| F|  |TAMS 0x000000008ce00000, 0x000000008ce00000| Untracked 
| 207|0x000000008cf00000, 0x000000008cf00000, 0x000000008d000000|  0%| F|  |TAMS 0x000000008cf00000, 0x000000008cf00000| Untracked 
| 208|0x000000008d000000, 0x000000008d000000, 0x000000008d100000|  0%| F|  |TAMS 0x000000008d000000, 0x000000008d000000| Untracked 
| 209|0x000000008d100000, 0x000000008d100000, 0x000000008d200000|  0%| F|  |TAMS 0x000000008d100000, 0x000000008d100000| Untracked 
| 210|0x000000008d200000, 0x000000008d200000, 0x000000008d300000|  0%| F|  |TAMS 0x000000008d200000, 0x000000008d200000| Untracked 
| 211|0x000000008d300000, 0x000000008d300000, 0x000000008d400000|  0%| F|  |TAMS 0x000000008d300000, 0x000000008d300000| Untracked 
| 212|0x000000008d400000, 0x000000008d400000, 0x000000008d500000|  0%| F|  |TAMS 0x000000008d400000, 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d500000, 0x000000008d600000|  0%| F|  |TAMS 0x000000008d500000, 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d600000, 0x000000008d700000|  0%| F|  |TAMS 0x000000008d600000, 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d700000, 0x000000008d800000|  0%| F|  |TAMS 0x000000008d700000, 0x000000008d700000| Untracked 
| 216|0x000000008d800000, 0x000000008d800000, 0x000000008d900000|  0%| F|  |TAMS 0x000000008d800000, 0x000000008d800000| Untracked 
| 217|0x000000008d900000, 0x000000008d900000, 0x000000008da00000|  0%| F|  |TAMS 0x000000008d900000, 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008da00000, 0x000000008db00000|  0%| F|  |TAMS 0x000000008da00000, 0x000000008da00000| Untracked 
| 219|0x000000008db00000, 0x000000008db00000, 0x000000008dc00000|  0%| F|  |TAMS 0x000000008db00000, 0x000000008db00000| Untracked 
| 220|0x000000008dc00000, 0x000000008dc00000, 0x000000008dd00000|  0%| F|  |TAMS 0x000000008dc00000, 0x000000008dc00000| Untracked 
| 221|0x000000008dd00000, 0x000000008dd00000, 0x000000008de00000|  0%| F|  |TAMS 0x000000008dd00000, 0x000000008dd00000| Untracked 
| 222|0x000000008de00000, 0x000000008de00000, 0x000000008df00000|  0%| F|  |TAMS 0x000000008de00000, 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008df00000, 0x000000008e000000|  0%| F|  |TAMS 0x000000008df00000, 0x000000008df00000| Untracked 
| 224|0x000000008e000000, 0x000000008e000000, 0x000000008e100000|  0%| F|  |TAMS 0x000000008e000000, 0x000000008e000000| Untracked 
| 225|0x000000008e100000, 0x000000008e100000, 0x000000008e200000|  0%| F|  |TAMS 0x000000008e100000, 0x000000008e100000| Untracked 
| 226|0x000000008e200000, 0x000000008e200000, 0x000000008e300000|  0%| F|  |TAMS 0x000000008e200000, 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e380800, 0x000000008e400000| 50%| E|  |TAMS 0x000000008e300000, 0x000000008e300000| Complete 
| 228|0x000000008e400000, 0x000000008e500000, 0x000000008e500000|100%| E|CS|TAMS 0x000000008e400000, 0x000000008e400000| Complete 
| 229|0x000000008e500000, 0x000000008e600000, 0x000000008e600000|100%| E|CS|TAMS 0x000000008e500000, 0x000000008e500000| Complete 
| 230|0x000000008e600000, 0x000000008e700000, 0x000000008e700000|100%| E|CS|TAMS 0x000000008e600000, 0x000000008e600000| Complete 
| 231|0x000000008e700000, 0x000000008e800000, 0x000000008e800000|100%| E|CS|TAMS 0x000000008e700000, 0x000000008e700000| Complete 
| 232|0x000000008e800000, 0x000000008e900000, 0x000000008e900000|100%| E|CS|TAMS 0x000000008e800000, 0x000000008e800000| Complete 
| 233|0x000000008e900000, 0x000000008ea00000, 0x000000008ea00000|100%| E|CS|TAMS 0x000000008e900000, 0x000000008e900000| Complete 
| 234|0x000000008ea00000, 0x000000008eb00000, 0x000000008eb00000|100%| E|CS|TAMS 0x000000008ea00000, 0x000000008ea00000| Complete 
| 235|0x000000008eb00000, 0x000000008ec00000, 0x000000008ec00000|100%| E|CS|TAMS 0x000000008eb00000, 0x000000008eb00000| Complete 
| 236|0x000000008ec00000, 0x000000008ed00000, 0x000000008ed00000|100%| E|CS|TAMS 0x000000008ec00000, 0x000000008ec00000| Complete 
| 237|0x000000008ed00000, 0x000000008ee00000, 0x000000008ee00000|100%| E|CS|TAMS 0x000000008ed00000, 0x000000008ed00000| Complete 
| 238|0x000000008ee00000, 0x000000008ef00000, 0x000000008ef00000|100%| E|CS|TAMS 0x000000008ee00000, 0x000000008ee00000| Complete 
| 239|0x000000008ef00000, 0x000000008f000000, 0x000000008f000000|100%| E|CS|TAMS 0x000000008ef00000, 0x000000008ef00000| Complete 
| 240|0x000000008f000000, 0x000000008f100000, 0x000000008f100000|100%| E|CS|TAMS 0x000000008f000000, 0x000000008f000000| Complete 
| 241|0x000000008f100000, 0x000000008f200000, 0x000000008f200000|100%| E|CS|TAMS 0x000000008f100000, 0x000000008f100000| Complete 
| 242|0x000000008f200000, 0x000000008f300000, 0x000000008f300000|100%| E|CS|TAMS 0x000000008f200000, 0x000000008f200000| Complete 
| 243|0x000000008f300000, 0x000000008f400000, 0x000000008f400000|100%| E|CS|TAMS 0x000000008f300000, 0x000000008f300000| Complete 
| 244|0x000000008f400000, 0x000000008f500000, 0x000000008f500000|100%| E|CS|TAMS 0x000000008f400000, 0x000000008f400000| Complete 
| 245|0x000000008f500000, 0x000000008f600000, 0x000000008f600000|100%| E|CS|TAMS 0x000000008f500000, 0x000000008f500000| Complete 
| 246|0x000000008f600000, 0x000000008f700000, 0x000000008f700000|100%| E|CS|TAMS 0x000000008f600000, 0x000000008f600000| Complete 
| 247|0x000000008f700000, 0x000000008f800000, 0x000000008f800000|100%| E|CS|TAMS 0x000000008f700000, 0x000000008f700000| Complete 
| 248|0x000000008f800000, 0x000000008f900000, 0x000000008f900000|100%| E|CS|TAMS 0x000000008f800000, 0x000000008f800000| Complete 
| 249|0x000000008f900000, 0x000000008fa00000, 0x000000008fa00000|100%| E|CS|TAMS 0x000000008f900000, 0x000000008f900000| Complete 
| 250|0x000000008fa00000, 0x000000008fae0480, 0x000000008fb00000| 87%| S|CS|TAMS 0x000000008fa00000, 0x000000008fa00000| Complete 
| 251|0x000000008fb00000, 0x000000008fc00000, 0x000000008fc00000|100%| S|CS|TAMS 0x000000008fb00000, 0x000000008fb00000| Complete 
| 252|0x000000008fc00000, 0x000000008fd00000, 0x000000008fd00000|100%| E|CS|TAMS 0x000000008fc00000, 0x000000008fc00000| Complete 
| 253|0x000000008fd00000, 0x000000008fe00000, 0x000000008fe00000|100%| E|CS|TAMS 0x000000008fd00000, 0x000000008fd00000| Complete 

Card table byte_map: [0x0000014ff8cb0000,0x0000014ff90b0000] _byte_map_base: 0x0000014ff88b0000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000014ff0ecbf40, (CMBitMap*) 0x0000014ff0ecbf08
 Prev Bits: [0x0000014ffb4b0000, 0x0000014ffd4b0000)
 Next Bits: [0x0000014ff94b0000, 0x0000014ffb4b0000)

Polling page: 0x0000014ff0630000

Metaspace:

Usage:
  Non-class:    128.33 MB capacity,   125.71 MB ( 98%) used,     2.12 MB (  2%) free+waste,   515.88 KB ( <1%) overhead. 
      Class:     19.56 MB capacity,    17.88 MB ( 91%) used,     1.44 MB (  7%) free+waste,   245.44 KB (  1%) overhead. 
       Both:    147.89 MB capacity,   143.58 MB ( 97%) used,     3.56 MB (  2%) free+waste,   761.31 KB ( <1%) overhead. 

Virtual space:
  Non-class space:      130.00 MB reserved,     128.53 MB ( 99%) committed 
      Class space:      504.00 MB reserved,      19.61 MB (  4%) committed 
             Both:      634.00 MB reserved,     148.13 MB ( 23%) committed 

Chunk freelists:
   Non-Class:  1.38 KB
       Class:  256 bytes
        Both:  1.62 KB

CodeHeap 'non-profiled nmethods': size=120000Kb used=9768Kb max_used=9768Kb free=110231Kb
 bounds [0x0000014f87ad0000, 0x0000014f88460000, 0x0000014f8f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=40860Kb max_used=40860Kb free=79139Kb
 bounds [0x0000014f805a0000, 0x0000014f82d90000, 0x0000014f87ad0000]
CodeHeap 'non-nmethods': size=5760Kb used=2462Kb max_used=2521Kb free=3297Kb
 bounds [0x0000014f80000000, 0x0000014f80280000, 0x0000014f805a0000]
 total_blobs=17210 nmethods=16081 adapters=1037
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (10 events):
Event: 60.871 Thread 0x0000014f8f85a000 nmethod 16961 0x0000014f82d5a190 code [0x0000014f82d5a360, 0x0000014f82d5a758]
Event: 60.871 Thread 0x0000014f8f85a000 16963       3       org.jetbrains.kotlin.cfg.pseudocode.instructions.InstructionVisitor::<init> (5 bytes)
Event: 60.871 Thread 0x0000014f8f85a000 nmethod 16963 0x0000014f82d5a890 code [0x0000014f82d5aa20, 0x0000014f82d5ab78]
Event: 60.871 Thread 0x0000014f8f85a000 16964       3       org.jetbrains.kotlin.cfg.pseudocode.instructions.InstructionWithNext::getNextInstructions (11 bytes)
Event: 60.872 Thread 0x0000014f8f85a000 nmethod 16964 0x0000014f82d5ac10 code [0x0000014f82d5ae00, 0x0000014f82d5b448]
Event: 60.872 Thread 0x0000014f8f85a000 16954       3       org.jetbrains.kotlin.com.intellij.psi.impl.source.tree.LeafElement::getPsi (71 bytes)
Event: 60.874 Thread 0x0000014f8f85a000 nmethod 16954 0x0000014f82d5b690 code [0x0000014f82d5b980, 0x0000014f82d5c798]
Event: 60.874 Thread 0x0000014f8f85a000 16968       3       org.jetbrains.kotlin.cfg.ControlFlowInformationProviderImpl$$Lambda$2128/0x0000000100f7dc40::invoke (44 bytes)
Event: 60.874 Thread 0x0000014f8f85a000 nmethod 16968 0x0000014f82d5cc90 code [0x0000014f82d5ce60, 0x0000014f82d5d308]
Event: 60.874 Thread 0x0000014f8f85a000 16969       3       org.jetbrains.kotlin.cfg.ControlFlowInformationProviderImpl::markUninitializedVariables$lambda$1 (239 bytes)

GC Heap History (10 events):
Event: 53.413 GC heap before
{Heap before GC invocations=26 (full 0):
 garbage-first heap   total 260096K, used 219200K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 109 young (111616K), 10 survivors (10240K)
 Metaspace       used 142051K, capacity 146246K, committed 146440K, reserved 643072K
  class space    used 17954K, capacity 19619K, committed 19692K, reserved 516096K
}
Event: 53.436 GC heap after
{Heap after GC invocations=27 (full 0):
 garbage-first heap   total 260096K, used 127349K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 142051K, capacity 146246K, committed 146440K, reserved 643072K
  class space    used 17954K, capacity 19619K, committed 19692K, reserved 516096K
}
Event: 56.791 GC heap before
{Heap before GC invocations=27 (full 0):
 garbage-first heap   total 260096K, used 216437K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 101 young (103424K), 14 survivors (14336K)
 Metaspace       used 146289K, capacity 150727K, committed 150920K, reserved 647168K
  class space    used 18300K, capacity 20021K, committed 20076K, reserved 516096K
}
Event: 56.819 GC heap after
{Heap after GC invocations=28 (full 0):
 garbage-first heap   total 260096K, used 135705K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 146289K, capacity 150727K, committed 150920K, reserved 647168K
  class space    used 18300K, capacity 20021K, committed 20076K, reserved 516096K
}
Event: 57.104 GC heap before
{Heap before GC invocations=28 (full 0):
 garbage-first heap   total 260096K, used 157209K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 30 young (30720K), 8 survivors (8192K)
 Metaspace       used 146311K, capacity 150733K, committed 150920K, reserved 647168K
  class space    used 18301K, capacity 20022K, committed 20076K, reserved 516096K
}
Event: 57.121 GC heap after
{Heap after GC invocations=29 (full 0):
 garbage-first heap   total 260096K, used 139887K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 146311K, capacity 150733K, committed 150920K, reserved 647168K
  class space    used 18301K, capacity 20022K, committed 20076K, reserved 516096K
}
Event: 58.050 GC heap before
{Heap before GC invocations=30 (full 0):
 garbage-first heap   total 260096K, used 206447K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 73 young (74752K), 5 survivors (5120K)
 Metaspace       used 146666K, capacity 151107K, committed 151176K, reserved 649216K
  class space    used 18305K, capacity 20029K, committed 20076K, reserved 516096K
}
Event: 58.063 GC heap after
{Heap after GC invocations=31 (full 0):
 garbage-first heap   total 260096K, used 142937K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 146666K, capacity 151107K, committed 151176K, reserved 649216K
  class space    used 18305K, capacity 20029K, committed 20076K, reserved 516096K
}
Event: 58.121 GC heap before
{Heap before GC invocations=31 (full 0):
 garbage-first heap   total 260096K, used 144985K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 10 survivors (10240K)
 Metaspace       used 146741K, capacity 151171K, committed 151432K, reserved 649216K
  class space    used 18305K, capacity 20029K, committed 20076K, reserved 516096K
}
Event: 58.137 GC heap after
{Heap after GC invocations=32 (full 0):
 garbage-first heap   total 260096K, used 129476K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 146741K, capacity 151171K, committed 151432K, reserved 649216K
  class space    used 18305K, capacity 20029K, committed 20076K, reserved 516096K
}

Deoptimization events (10 events):
Event: 56.440 Thread 0x0000014f9022b800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000014f87fa62bc method=java.io.ObjectStreamClass.writeNonProxy(Ljava/io/ObjectOutputStream;)V @ 22 c2
Event: 56.441 Thread 0x0000014f9022b800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000014f87f65fd0 method=com.esotericsoftware.kryo.io.Output.writeBytes([BII)V @ 61 c2
Event: 56.441 Thread 0x0000014f9022b800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000014f88318864 method=com.esotericsoftware.kryo.io.Output.require(I)Z @ 10 c2
Event: 57.828 Thread 0x0000014f90f93800 Uncommon trap: reason=null_check action=make_not_entrant pc=0x0000014f8829d180 method=java.lang.ThreadLocal$ThreadLocalMap.getEntryAfterMiss(Ljava/lang/ThreadLocal;ILjava/lang/ThreadLocal$ThreadLocalMap$Entry;)Ljava/lang/ThreadLocal$ThreadLocalMap$Entry; 
Event: 57.934 Thread 0x0000014f90f93800 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x0000014f8841ee04 method=java.util.regex.Pattern$Slice.match(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Z @ 18 c2
Event: 57.934 Thread 0x0000014f90f93800 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x0000014f8841ee04 method=java.util.regex.Pattern$Slice.match(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Z @ 18 c2
Event: 57.935 Thread 0x0000014f90f93800 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x0000014f8841ee04 method=java.util.regex.Pattern$Slice.match(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Z @ 18 c2
Event: 58.158 Thread 0x0000014f90f93800 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x0000014f8841ee04 method=java.util.regex.Pattern$Slice.match(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Z @ 18 c2
Event: 58.373 Thread 0x0000014f90f93800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000014f8843f2c0 method=org.jetbrains.org.objectweb.asm.ClassReader.readMethod(Lorg/jetbrains/org/objectweb/asm/ClassVisitor;Lorg/jetbrains/org/objectweb/asm/Context;I)I @ 604 c2
Event: 58.386 Thread 0x0000014f90f93800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000014f87d6aa30 method=org.jetbrains.org.objectweb.asm.ClassReader.readElementValue(Lorg/jetbrains/org/objectweb/asm/AnnotationVisitor;ILjava/lang/String;[C)I @ 18 c2

Classes redefined (0 events):
No events

Internal exceptions (10 events):
Event: 56.014 Thread 0x0000014f90f93800 Exception <a 'sun/nio/fs/WindowsException'{0x000000008c2c8a10}> (0x000000008c2c8a10) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 611]
Event: 56.020 Thread 0x0000014f90f93800 Exception <a 'sun/nio/fs/WindowsException'{0x000000008c2d02e0}> (0x000000008c2d02e0) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 611]
Event: 56.671 Thread 0x0000014f90f93800 Exception <a 'sun/nio/fs/WindowsException'{0x000000008aea06c0}> (0x000000008aea06c0) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 611]
Event: 56.671 Thread 0x0000014f90f93800 Exception <a 'sun/nio/fs/WindowsException'{0x000000008aea26e0}> (0x000000008aea26e0) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 611]
Event: 56.718 Thread 0x0000014f90f93800 Exception <a 'sun/nio/fs/WindowsException'{0x000000008ab4cd58}> (0x000000008ab4cd58) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 611]
Event: 56.726 Thread 0x0000014f90f93800 Exception <a 'sun/nio/fs/WindowsException'{0x000000008ab57af8}> (0x000000008ab57af8) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 611]
Event: 56.727 Thread 0x0000014f90f93800 Exception <a 'sun/nio/fs/WindowsException'{0x000000008ab58998}> (0x000000008ab58998) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 611]
Event: 56.733 Thread 0x0000014f90f93800 Exception <a 'sun/nio/fs/WindowsException'{0x000000008ab63580}> (0x000000008ab63580) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 611]
Event: 56.734 Thread 0x0000014f90f93800 Exception <a 'sun/nio/fs/WindowsException'{0x000000008ab64420}> (0x000000008ab64420) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 611]
Event: 57.828 Thread 0x0000014f90f93800 Implicit null exception at 0x0000014f8829d08c to 0x0000014f8829d164

Events (10 events):
Event: 58.137 Executing VM operation: G1CollectForAllocation done
Event: 58.158 Thread 0x0000014f90f93800 Uncommon trap: trap_request=0xffffffbe fr.pc=0x0000014f8841ee04 relative=0x0000000000001344
Event: 58.158 Thread 0x0000014f90f93800 DEOPT PACKING pc=0x0000014f8841ee04 sp=0x0000002e67ff08c0
Event: 58.158 Thread 0x0000014f90f93800 DEOPT UNPACKING pc=0x0000014f800265af sp=0x0000002e67ff06a0 mode 2
Event: 58.373 Thread 0x0000014f90f93800 Uncommon trap: trap_request=0xffffff4d fr.pc=0x0000014f8843f2c0 relative=0x0000000000004020
Event: 58.373 Thread 0x0000014f90f93800 DEOPT PACKING pc=0x0000014f8843f2c0 sp=0x0000002e67ff1430
Event: 58.373 Thread 0x0000014f90f93800 DEOPT UNPACKING pc=0x0000014f800265af sp=0x0000002e67ff13d8 mode 2
Event: 58.386 Thread 0x0000014f90f93800 Uncommon trap: trap_request=0xffffff4d fr.pc=0x0000014f87d6aa30 relative=0x00000000000019b0
Event: 58.386 Thread 0x0000014f90f93800 DEOPT PACKING pc=0x0000014f87d6aa30 sp=0x0000002e67feee20
Event: 58.386 Thread 0x0000014f90f93800 DEOPT UNPACKING pc=0x0000014f800265af sp=0x0000002e67feeda0 mode 2


Dynamic libraries:
0x00007ff659a60000 - 0x00007ff659a70000 	C:\Program Files\Java\jdk-11.0.25\bin\java.exe
0x00007fff7f780000 - 0x00007fff7f9e5000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fff7ebe0000 - 0x00007fff7eca9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fff7ccf0000 - 0x00007fff7d0d8000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fff7d340000 - 0x00007fff7d48b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff6c250000 - 0x00007fff6c26b000 	C:\Program Files\Java\jdk-11.0.25\bin\VCRUNTIME140.dll
0x00007fff6c370000 - 0x00007fff6c389000 	C:\Program Files\Java\jdk-11.0.25\bin\jli.dll
0x00007fff7f490000 - 0x00007fff7f543000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fff7ed90000 - 0x00007fff7ee39000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fff7f3d0000 - 0x00007fff7f476000 	C:\WINDOWS\System32\sechost.dll
0x00007fff7d520000 - 0x00007fff7d635000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fff7f1a0000 - 0x00007fff7f36a000 	C:\WINDOWS\System32\USER32.dll
0x00007fff660e0000 - 0x00007fff6637a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007fff7d260000 - 0x00007fff7d287000 	C:\WINDOWS\System32\win32u.dll
0x00007fff7f710000 - 0x00007fff7f73b000 	C:\WINDOWS\System32\GDI32.dll
0x00007fff7c8d0000 - 0x00007fff7ca07000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fff7d290000 - 0x00007fff7d333000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fff6de40000 - 0x00007fff6de4b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fff7ecd0000 - 0x00007fff7ed00000 	C:\WINDOWS\System32\IMM32.DLL
0x00007fff764a0000 - 0x00007fff764ac000 	C:\Program Files\Java\jdk-11.0.25\bin\vcruntime140_1.dll
0x00007fff51890000 - 0x00007fff5191e000 	C:\Program Files\Java\jdk-11.0.25\bin\msvcp140.dll
0x00007fff0ec90000 - 0x00007fff0f7f3000 	C:\Program Files\Java\jdk-11.0.25\bin\server\jvm.dll
0x00007fff7dbf0000 - 0x00007fff7dbf8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007fff58c00000 - 0x00007fff58c0a000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007fff666f0000 - 0x00007fff66725000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fff7eeb0000 - 0x00007fff7ef24000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fff7b7b0000 - 0x00007fff7b7cb000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fff76440000 - 0x00007fff76450000 	C:\Program Files\Java\jdk-11.0.25\bin\verify.dll
0x00007fff76bf0000 - 0x00007fff76e31000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fff7d860000 - 0x00007fff7dbe5000 	C:\WINDOWS\System32\combase.dll
0x00007fff7f0b0000 - 0x00007fff7f191000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fff61080000 - 0x00007fff610b9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fff7ca10000 - 0x00007fff7caa9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fff67cc0000 - 0x00007fff67ce8000 	C:\Program Files\Java\jdk-11.0.25\bin\java.dll
0x00007fff6f470000 - 0x00007fff6f47a000 	C:\Program Files\Java\jdk-11.0.25\bin\jimage.dll
0x00007fff6c240000 - 0x00007fff6c24e000 	C:\Program Files\Java\jdk-11.0.25\bin\instrument.dll
0x00007fff6bdd0000 - 0x00007fff6bde7000 	C:\Program Files\Java\jdk-11.0.25\bin\zip.dll
0x00007fff7e140000 - 0x00007fff7e882000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fff7d0e0000 - 0x00007fff7d254000 	C:\WINDOWS\System32\wintypes.dll
0x00007fff7a590000 - 0x00007fff7ade8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fff7f580000 - 0x00007fff7f671000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fff7d640000 - 0x00007fff7d6aa000 	C:\WINDOWS\System32\shlwapi.dll
0x00007fff7c7e0000 - 0x00007fff7c80f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff6bbd0000 - 0x00007fff6bbe9000 	C:\Program Files\Java\jdk-11.0.25\bin\net.dll
0x00007fff76ff0000 - 0x00007fff7710e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007fff7bd20000 - 0x00007fff7bd8a000 	C:\WINDOWS\system32\mswsock.dll
0x00007fff67d80000 - 0x00007fff67d92000 	C:\Program Files\Java\jdk-11.0.25\bin\nio.dll
0x00007fff6c390000 - 0x00007fff6c3b7000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00000000718d0000 - 0x0000000071943000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007fff6c200000 - 0x00007fff6c209000 	C:\Program Files\Java\jdk-11.0.25\bin\management.dll
0x00007fff6bc90000 - 0x00007fff6bc9b000 	C:\Program Files\Java\jdk-11.0.25\bin\management_ext.dll
0x00007fff7bfd0000 - 0x00007fff7bfeb000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fff7b710000 - 0x00007fff7b74a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fff7bdc0000 - 0x00007fff7bdeb000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fff7c7b0000 - 0x00007fff7c7d6000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007fff7bff0000 - 0x00007fff7bffc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fff7b150000 - 0x00007fff7b183000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fff7f480000 - 0x00007fff7f48a000 	C:\WINDOWS\System32\NSI.dll
0x00007fff6dd30000 - 0x00007fff6dd4f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007fff6dd00000 - 0x00007fff6dd25000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007fff7b1e0000 - 0x00007fff7b307000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x0000000071850000 - 0x00000000718c3000 	C:\Users\<USER>\AppData\Local\Temp\native-platform18239795535945495527dir\gradle-fileevents.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-11.0.25\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;C:\Program Files\Java\jdk-11.0.25\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform18239795535945495527dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=512m -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 528482304                                 {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxMetaspaceSize                         = 536870912                                 {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5836300                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122910970                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122910970                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-11.0.25
PATH=C:\Gradle\gradle-8.13\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\Java\jdk-11.0.25\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\SqlCmd\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64\compiler;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Intel\WiFi\bin\;C:\Program Files\Common Files\Intel\WirelessCommon\;C:\ProgramData\chocolatey\bin;C:\Python312\Scripts;C:\Python312;C:\Program Files\dotnet\;C:\xampp\php\php.exe;C:\php;C:\tools\php84;C:\xampp\mysql\bin;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\ncat;C:\curl;C:\Users\<USER>\AppData\Roaming\npm\node_modules;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Program Files\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundatio;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\Gradle\gradle-8.13\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\Scripts;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files (x86)\Nmap;C:\xampp\mysql\b;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\MongoDB\Server\7.0\bin;C:\Program Files\MongoDB\Server\8.0\bin;;C:\PostgreSQL\16\bin;C:\Program Files\SqlCmd\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\Com
USERNAME=Sheldon
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 10, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 11 , 64 bit Build 26100 (10.0.26100.4202)

CPU:total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 142 stepping 10 microcode 0xf6, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, rtm, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, fma

Memory: 4k page, system-wide physical 16222M (1133M free)
TotalPageFile size 21072M (AvailPageFile size 3M)
current process WorkingSet (physical memory assigned to process): 651M, peak: 657M
current process commit charge ("private bytes"): 693M, peak: 699M

vm_info: Java HotSpot(TM) 64-Bit Server VM (11.0.25+9-LTS-256) for windows-amd64 JRE (11.0.25+9-LTS-256), built on Sep 30 2024 06:30:20 by "mach5one" with MS VC++ 17.6 (VS2022)

END.
