#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 111149056 bytes for Failed to commit area from 0x0000000094c00000 to 0x000000009b600000 of length 111149056.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (./open/src/hotspot/os/windows/os_windows.cpp:3298), pid=25456, tid=26012
#
# JRE version: Java(TM) SE Runtime Environment 18.9 (11.0.25+9) (build 11.0.25+9-LTS-256)
# Java VM: Java HotSpot(TM) 64-Bit Server VM 18.9 (11.0.25+9-LTS-256, mixed mode, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=512m -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: Intel(R) Core(TM) i7-8650U CPU @ 1.90GHz, 8 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Tue Jul  1 12:44:35 2025 E. Africa Standard Time elapsed time: 92.599407 seconds (0d 0h 1m 32s)

---------------  T H R E A D  ---------------

Current thread (0x000001fb5892e000):  VMThread "VM Thread" [stack: 0x0000000e75600000,0x0000000e75700000] [id=26012]

Stack: [0x0000000e75600000,0x0000000e75700000]
[error occurred during error reporting (printing stack bounds), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x000001fb41750f2d]

Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x65824a]
V  [jvm.dll+0x79ae8f]
V  [jvm.dll+0x79c559]
V  [jvm.dll+0x79cb65]
V  [jvm.dll+0x79cb1b]
V  [jvm.dll+0x65775d]
V  [jvm.dll+0x657e48]
C  [ntdll.dll+0x16623f]
C  [ntdll.dll+0x745d7]
C  [ntdll.dll+0x165b7e]
C  0x000001fb41750f2d

VM_Operation (0x0000000e78ff96f0): G1CollectForAllocation, mode: safepoint, requested by thread 0x000001fb5e071000


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001fb5e5de6e0, length=142, elements={
0x000001fb3a059800, 0x000001fb58935000, 0x000001fb58940800, 0x000001fb58951000,
0x000001fb58952000, 0x000001fb58955800, 0x000001fb58957000, 0x000001fb589b3000,
0x000001fb589b4800, 0x000001fb58b08800, 0x000001fb5aac6000, 0x000001fb5ac3b800,
0x000001fb59b1a000, 0x000001fb5a42e800, 0x000001fb59d30800, 0x000001fb59bb4000,
0x000001fb5a088800, 0x000001fb59ada000, 0x000001fb5aeda800, 0x000001fb5aedb800,
0x000001fb5b45a800, 0x000001fb5b459000, 0x000001fb5b458800, 0x000001fb5b45b800,
0x000001fb5b45c800, 0x000001fb5b45a000, 0x000001fb5b45e000, 0x000001fb5b45f000,
0x000001fb5b45d000, 0x000001fb5b5e1800, 0x000001fb5b5e3000, 0x000001fb5b5e0800,
0x000001fb5b5e7000, 0x000001fb5b5e4800, 0x000001fb5b5e8000, 0x000001fb5b5e8800,
0x000001fb5b5e9800, 0x000001fb5b5ed000, 0x000001fb5b5ea800, 0x000001fb5b5ed800,
0x000001fb5b5ec000, 0x000001fb5b5eb000, 0x000001fb5b5ee800, 0x000001fb5b5ef800,
0x000001fb5e071800, 0x000001fb5e072800, 0x000001fb5e071000, 0x000001fb5e070000,
0x000001fb5e076000, 0x000001fb5e074000, 0x000001fb5e075000, 0x000001fb5e073800,
0x000001fb5e077800, 0x000001fb5e078000, 0x000001fb5e079000, 0x000001fb5e076800,
0x000001fb5e07a000, 0x000001fb5e07d000, 0x000001fb5e07a800, 0x000001fb5e07e000,
0x000001fb5e07c800, 0x000001fb5e07b800, 0x000001fb5e07e800, 0x000001fb5d393000,
0x000001fb5d393800, 0x000001fb5d394800, 0x000001fb63704800, 0x000001fb63708000,
0x000001fb63705800, 0x000001fb63709800, 0x000001fb6370b000, 0x000001fb63708800,
0x000001fb6370a800, 0x000001fb6370c000, 0x000001fb6370d800, 0x000001fb6370e800,
0x000001fb6370f000, 0x000001fb6370d000, 0x000001fb63710000, 0x000001fb63713800,
0x000001fb63711000, 0x000001fb63711800, 0x000001fb63712800, 0x000001fb63569000,
0x000001fb6356b800, 0x000001fb6356c800, 0x000001fb6356a000, 0x000001fb6356d000,
0x000001fb6356a800, 0x000001fb6356e800, 0x000001fb6356e000, 0x000001fb6356f800,
0x000001fb63078000, 0x000001fb6307a800, 0x000001fb6307b000, 0x000001fb63078800,
0x000001fb6307c000, 0x000001fb63079800, 0x000001fb6307f000, 0x000001fb6307d000,
0x000001fb6307e800, 0x000001fb63080000, 0x000001fb6307d800, 0x000001fb63081800,
0x000001fb63082800, 0x000001fb63083800, 0x000001fb63081000, 0x000001fb63084000,
0x000001fb63085000, 0x000001fb63085800, 0x000001fb63086800, 0x000001fb5fd3c000,
0x000001fb5fd3a800, 0x000001fb5fd3e000, 0x000001fb5fd3d000, 0x000001fb5fd3b800,
0x000001fb5fd41000, 0x000001fb5fd3e800, 0x000001fb5fd42000, 0x000001fb5fd40800,
0x000001fb5fd3f800, 0x000001fb5fd45000, 0x000001fb5fd43000, 0x000001fb5fd46000,
0x000001fb5fd43800, 0x000001fb5fd44800, 0x000001fb5fd48800, 0x000001fb5fd49800,
0x000001fb5fd47800, 0x000001fb5fd47000, 0x000001fb5d589800, 0x000001fb5d589000,
0x000001fb5d588000, 0x000001fb5d587000, 0x000001fb5d58b000, 0x000001fb5d58c000,
0x000001fb5d58a800, 0x000001fb5d58f800, 0x000001fb5d58d000, 0x000001fb5d590000,
0x000001fb61982000, 0x000001fb6198b000
}

Java Threads: ( => current thread )
  0x000001fb3a059800 JavaThread "main" [_thread_blocked, id=26224, stack(0x0000000e75000000,0x0000000e75100000)]
  0x000001fb58935000 JavaThread "Reference Handler" daemon [_thread_blocked, id=22576, stack(0x0000000e75700000,0x0000000e75800000)]
  0x000001fb58940800 JavaThread "Finalizer" daemon [_thread_blocked, id=716, stack(0x0000000e75800000,0x0000000e75900000)]
  0x000001fb58951000 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=24832, stack(0x0000000e75900000,0x0000000e75a00000)]
  0x000001fb58952000 JavaThread "Attach Listener" daemon [_thread_blocked, id=26580, stack(0x0000000e75a00000,0x0000000e75b00000)]
  0x000001fb58955800 JavaThread "Service Thread" daemon [_thread_blocked, id=3952, stack(0x0000000e75b00000,0x0000000e75c00000)]
  0x000001fb58957000 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=24984, stack(0x0000000e75c00000,0x0000000e75d00000)]
  0x000001fb589b3000 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=24976, stack(0x0000000e75d00000,0x0000000e75e00000)]
  0x000001fb589b4800 JavaThread "Sweeper thread" daemon [_thread_blocked, id=22792, stack(0x0000000e75e00000,0x0000000e75f00000)]
  0x000001fb58b08800 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=2268, stack(0x0000000e75f00000,0x0000000e76000000)]
  0x000001fb5aac6000 JavaThread "Daemon health stats" [_thread_blocked, id=2892, stack(0x0000000e76200000,0x0000000e76300000)]
  0x000001fb5ac3b800 JavaThread "Incoming local TCP Connector on port 39353" [_thread_in_native, id=15668, stack(0x0000000e76000000,0x0000000e76100000)]
  0x000001fb59b1a000 JavaThread "Daemon periodic checks" [_thread_blocked, id=22684, stack(0x0000000e76900000,0x0000000e76a00000)]
  0x000001fb5a42e800 JavaThread "Daemon" [_thread_blocked, id=13412, stack(0x0000000e76a00000,0x0000000e76b00000)]
  0x000001fb59d30800 JavaThread "Handler for socket connection from /127.0.0.1:39353 to /127.0.0.1:39354" [_thread_in_native, id=2396, stack(0x0000000e76b00000,0x0000000e76c00000)]
  0x000001fb59bb4000 JavaThread "Cancel handler" [_thread_blocked, id=25732, stack(0x0000000e76c00000,0x0000000e76d00000)]
  0x000001fb5a088800 JavaThread "Daemon worker" [_thread_blocked, id=25444, stack(0x0000000e76d00000,0x0000000e76e00000)]
  0x000001fb59ada000 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:39353 to /127.0.0.1:39354" [_thread_blocked, id=25328, stack(0x0000000e76e00000,0x0000000e76f00000)]
  0x000001fb5aeda800 JavaThread "Stdin handler" [_thread_blocked, id=19820, stack(0x0000000e76f00000,0x0000000e77000000)]
  0x000001fb5aedb800 JavaThread "Daemon client event forwarder" [_thread_blocked, id=26256, stack(0x0000000e77000000,0x0000000e77100000)]
  0x000001fb5b45a800 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=1272, stack(0x0000000e77400000,0x0000000e77500000)]
  0x000001fb5b459000 JavaThread "File lock request listener" [_thread_in_native, id=26268, stack(0x0000000e77500000,0x0000000e77600000)]
  0x000001fb5b458800 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)" [_thread_blocked, id=19836, stack(0x0000000e77600000,0x0000000e77700000)]
  0x000001fb5b45b800 JavaThread "Problems report writer" [_thread_blocked, id=23976, stack(0x0000000e77800000,0x0000000e77900000)]
  0x000001fb5b45c800 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\android\.gradle\8.12\fileHashes)" [_thread_blocked, id=25876, stack(0x0000000e77900000,0x0000000e77a00000)]
  0x000001fb5b45a000 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=24056, stack(0x0000000e77a00000,0x0000000e77b00000)]
  0x000001fb5b45e000 JavaThread "File watcher server" daemon [_thread_in_native, id=26376, stack(0x0000000e77b00000,0x0000000e77c00000)]
  0x000001fb5b45f000 JavaThread "File watcher consumer" daemon [_thread_blocked, id=23436, stack(0x0000000e77c00000,0x0000000e77d00000)]
  0x000001fb5b45d000 JavaThread "jar transforms" [_thread_blocked, id=10636, stack(0x0000000e77d00000,0x0000000e77e00000)]
  0x000001fb5b5e1800 JavaThread "jar transforms Thread 2" [_thread_blocked, id=25820, stack(0x0000000e77e00000,0x0000000e77f00000)]
  0x000001fb5b5e3000 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\android\.gradle\8.12\checksums)" [_thread_blocked, id=25536, stack(0x0000000e77f00000,0x0000000e78000000)]
  0x000001fb5b5e0800 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)" [_thread_blocked, id=8116, stack(0x0000000e78000000,0x0000000e78100000)]
  0x000001fb5b5e7000 JavaThread "jar transforms Thread 3" [_thread_blocked, id=11992, stack(0x0000000e78100000,0x0000000e78200000)]
  0x000001fb5b5e4800 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.12\md-rule)" [_thread_blocked, id=3992, stack(0x0000000e78200000,0x0000000e78300000)]
  0x000001fb5b5e8000 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.12\md-supplier)" [_thread_blocked, id=23796, stack(0x0000000e78300000,0x0000000e78400000)]
  0x000001fb5b5e8800 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)" [_thread_blocked, id=10204, stack(0x0000000e78400000,0x0000000e78500000)]
  0x000001fb5b5e9800 JavaThread "Unconstrained build operations" [_thread_blocked, id=20636, stack(0x0000000e77700000,0x0000000e77800000)]
  0x000001fb5b5ed000 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=10468, stack(0x0000000e78500000,0x0000000e78600000)]
  0x000001fb5b5ea800 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=19988, stack(0x0000000e78600000,0x0000000e78700000)]
  0x000001fb5b5ed800 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=24988, stack(0x0000000e78700000,0x0000000e78800000)]
  0x000001fb5b5ec000 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=23856, stack(0x0000000e78800000,0x0000000e78900000)]
  0x000001fb5b5eb000 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=8976, stack(0x0000000e78900000,0x0000000e78a00000)]
  0x000001fb5b5ee800 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=3360, stack(0x0000000e78b00000,0x0000000e78c00000)]
  0x000001fb5b5ef800 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=25756, stack(0x0000000e78c00000,0x0000000e78d00000)]
  0x000001fb5e071800 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=17960, stack(0x0000000e78d00000,0x0000000e78e00000)]
  0x000001fb5e072800 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=23916, stack(0x0000000e78e00000,0x0000000e78f00000)]
  0x000001fb5e071000 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=26048, stack(0x0000000e78f00000,0x0000000e79000000)]
  0x000001fb5e070000 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=23932, stack(0x0000000e79000000,0x0000000e79100000)]
  0x000001fb5e076000 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=22912, stack(0x0000000e79100000,0x0000000e79200000)]
  0x000001fb5e074000 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=26316, stack(0x0000000e79200000,0x0000000e79300000)]
  0x000001fb5e075000 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=25256, stack(0x0000000e79300000,0x0000000e79400000)]
  0x000001fb5e073800 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=15852, stack(0x0000000e79400000,0x0000000e79500000)]
  0x000001fb5e077800 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=26512, stack(0x0000000e79500000,0x0000000e79600000)]
  0x000001fb5e078000 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=24444, stack(0x0000000e79600000,0x0000000e79700000)]
  0x000001fb5e079000 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=26344, stack(0x0000000e79700000,0x0000000e79800000)]
  0x000001fb5e076800 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=25340, stack(0x0000000e79800000,0x0000000e79900000)]
  0x000001fb5e07a000 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=9448, stack(0x0000000e79900000,0x0000000e79a00000)]
  0x000001fb5e07d000 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=1820, stack(0x0000000e79a00000,0x0000000e79b00000)]
  0x000001fb5e07a800 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=22284, stack(0x0000000e79b00000,0x0000000e79c00000)]
  0x000001fb5e07e000 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=14408, stack(0x0000000e79c00000,0x0000000e79d00000)]
  0x000001fb5e07c800 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=20336, stack(0x0000000e79d00000,0x0000000e79e00000)]
  0x000001fb5e07b800 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=1456, stack(0x0000000e79e00000,0x0000000e79f00000)]
  0x000001fb5e07e800 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=2164, stack(0x0000000e79f00000,0x0000000e7a000000)]
  0x000001fb5d393000 JavaThread "build event listener" [_thread_blocked, id=16000, stack(0x0000000e7a000000,0x0000000e7a100000)]
  0x000001fb5d393800 JavaThread "Memory manager" [_thread_blocked, id=22824, stack(0x0000000e78a00000,0x0000000e78b00000)]
  0x000001fb5d394800 JavaThread "Kotlin DSL Writer" [_thread_blocked, id=20644, stack(0x0000000e7a800000,0x0000000e7a900000)]
  0x000001fb63704800 JavaThread "ForkJoinPool.commonPool-worker-3" daemon [_thread_blocked, id=12192, stack(0x0000000e7a900000,0x0000000e7aa00000)]
  0x000001fb63708000 JavaThread "ForkJoinPool.commonPool-worker-5" daemon [_thread_blocked, id=18488, stack(0x0000000e7aa00000,0x0000000e7ab00000)]
  0x000001fb63705800 JavaThread "ForkJoinPool.commonPool-worker-7" daemon [_thread_blocked, id=11232, stack(0x0000000e7ab00000,0x0000000e7ac00000)]
  0x000001fb63709800 JavaThread "ForkJoinPool.commonPool-worker-9" daemon [_thread_blocked, id=7096, stack(0x0000000e7ac00000,0x0000000e7ad00000)]
  0x000001fb6370b000 JavaThread "ForkJoinPool.commonPool-worker-11" daemon [_thread_blocked, id=5016, stack(0x0000000e7ad00000,0x0000000e7ae00000)]
  0x000001fb63708800 JavaThread "ForkJoinPool.commonPool-worker-13" daemon [_thread_blocked, id=16112, stack(0x0000000e7ae00000,0x0000000e7af00000)]
  0x000001fb6370a800 JavaThread "ForkJoinPool.commonPool-worker-15" daemon [_thread_blocked, id=25196, stack(0x0000000e7af00000,0x0000000e7b000000)]
  0x000001fb6370c000 JavaThread "Exec process" [_thread_blocked, id=25552, stack(0x0000000e7b000000,0x0000000e7b100000)]
  0x000001fb6370d800 JavaThread "Exec process Thread 2" [_thread_blocked, id=22116, stack(0x0000000e7b100000,0x0000000e7b200000)]
  0x000001fb6370e800 JavaThread "Exec process Thread 3" [_thread_blocked, id=18828, stack(0x0000000e7b200000,0x0000000e7b300000)]
  0x000001fb6370f000 JavaThread "included builds" [_thread_blocked, id=6840, stack(0x0000000e7b300000,0x0000000e7b400000)]
  0x000001fb6370d000 JavaThread "Execution worker" [_thread_blocked, id=17028, stack(0x0000000e7b400000,0x0000000e7b500000)]
  0x000001fb63710000 JavaThread "Execution worker Thread 2" [_thread_blocked, id=19844, stack(0x0000000e7b500000,0x0000000e7b600000)]
  0x000001fb63713800 JavaThread "Execution worker Thread 3" [_thread_blocked, id=12708, stack(0x0000000e7b600000,0x0000000e7b700000)]
  0x000001fb63711000 JavaThread "Execution worker Thread 4" [_thread_blocked, id=23632, stack(0x0000000e7b700000,0x0000000e7b800000)]
  0x000001fb63711800 JavaThread "Execution worker Thread 5" [_thread_blocked, id=19152, stack(0x0000000e7b800000,0x0000000e7b900000)]
  0x000001fb63712800 JavaThread "Execution worker Thread 6" [_thread_blocked, id=18452, stack(0x0000000e7b900000,0x0000000e7ba00000)]
  0x000001fb63569000 JavaThread "Execution worker Thread 7" [_thread_blocked, id=21960, stack(0x0000000e7ba00000,0x0000000e7bb00000)]
  0x000001fb6356b800 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\node_modules\@react-native\gradle-plugin\.gradle\8.12\executionHistory)" [_thread_blocked, id=1376, stack(0x0000000e7bb00000,0x0000000e7bc00000)]
  0x000001fb6356c800 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=25720, stack(0x0000000e7bc00000,0x0000000e7bd00000)]
  0x000001fb6356a000 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=20100, stack(0x0000000e7bd00000,0x0000000e7be00000)]
  0x000001fb6356d000 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=17616, stack(0x0000000e7be00000,0x0000000e7bf00000)]
  0x000001fb6356a800 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=12340, stack(0x0000000e7bf00000,0x0000000e7c000000)]
  0x000001fb6356e800 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=15776, stack(0x0000000e7c000000,0x0000000e7c100000)]
  0x000001fb6356e000 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=22088, stack(0x0000000e7c100000,0x0000000e7c200000)]
  0x000001fb6356f800 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=26016, stack(0x0000000e7c200000,0x0000000e7c300000)]
  0x000001fb63078000 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=26524, stack(0x0000000e7c300000,0x0000000e7c400000)]
  0x000001fb6307a800 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=25432, stack(0x0000000e7c400000,0x0000000e7c500000)]
  0x000001fb6307b000 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=11056, stack(0x0000000e7c500000,0x0000000e7c600000)]
  0x000001fb63078800 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=7576, stack(0x0000000e7c600000,0x0000000e7c700000)]
  0x000001fb6307c000 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=3008, stack(0x0000000e7c700000,0x0000000e7c800000)]
  0x000001fb63079800 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=12864, stack(0x0000000e7c800000,0x0000000e7c900000)]
  0x000001fb6307f000 JavaThread "Unconstrained build operations Thread 41" [_thread_blocked, id=24704, stack(0x0000000e7c900000,0x0000000e7ca00000)]
  0x000001fb6307d000 JavaThread "Unconstrained build operations Thread 42" [_thread_blocked, id=9808, stack(0x0000000e7ca00000,0x0000000e7cb00000)]
  0x000001fb6307e800 JavaThread "Unconstrained build operations Thread 43" [_thread_blocked, id=13148, stack(0x0000000e7cb00000,0x0000000e7cc00000)]
  0x000001fb63080000 JavaThread "Unconstrained build operations Thread 44" [_thread_blocked, id=20200, stack(0x0000000e7cc00000,0x0000000e7cd00000)]
  0x000001fb6307d800 JavaThread "Unconstrained build operations Thread 45" [_thread_blocked, id=19452, stack(0x0000000e7cd00000,0x0000000e7ce00000)]
  0x000001fb63081800 JavaThread "Unconstrained build operations Thread 46" [_thread_blocked, id=14804, stack(0x0000000e7ce00000,0x0000000e7cf00000)]
  0x000001fb63082800 JavaThread "Unconstrained build operations Thread 47" [_thread_blocked, id=16888, stack(0x0000000e7cf00000,0x0000000e7d000000)]
  0x000001fb63083800 JavaThread "Unconstrained build operations Thread 48" [_thread_blocked, id=8548, stack(0x0000000e7d000000,0x0000000e7d100000)]
  0x000001fb63081000 JavaThread "Unconstrained build operations Thread 49" [_thread_blocked, id=22500, stack(0x0000000e7d100000,0x0000000e7d200000)]
  0x000001fb63084000 JavaThread "Unconstrained build operations Thread 50" [_thread_blocked, id=5908, stack(0x0000000e7d200000,0x0000000e7d300000)]
  0x000001fb63085000 JavaThread "Unconstrained build operations Thread 51" [_thread_blocked, id=19912, stack(0x0000000e7d300000,0x0000000e7d400000)]
  0x000001fb63085800 JavaThread "Unconstrained build operations Thread 52" [_thread_blocked, id=15924, stack(0x0000000e7d400000,0x0000000e7d500000)]
  0x000001fb63086800 JavaThread "Unconstrained build operations Thread 53" [_thread_blocked, id=25408, stack(0x0000000e7d500000,0x0000000e7d600000)]
  0x000001fb5fd3c000 JavaThread "Unconstrained build operations Thread 54" [_thread_blocked, id=9116, stack(0x0000000e7d600000,0x0000000e7d700000)]
  0x000001fb5fd3a800 JavaThread "Unconstrained build operations Thread 55" [_thread_blocked, id=15004, stack(0x0000000e7d700000,0x0000000e7d800000)]
  0x000001fb5fd3e000 JavaThread "Unconstrained build operations Thread 56" [_thread_blocked, id=5604, stack(0x0000000e7d800000,0x0000000e7d900000)]
  0x000001fb5fd3d000 JavaThread "Unconstrained build operations Thread 57" [_thread_blocked, id=2720, stack(0x0000000e7d900000,0x0000000e7da00000)]
  0x000001fb5fd3b800 JavaThread "Unconstrained build operations Thread 58" [_thread_blocked, id=16232, stack(0x0000000e7da00000,0x0000000e7db00000)]
  0x000001fb5fd41000 JavaThread "Unconstrained build operations Thread 59" [_thread_blocked, id=14512, stack(0x0000000e7db00000,0x0000000e7dc00000)]
  0x000001fb5fd3e800 JavaThread "Unconstrained build operations Thread 60" [_thread_blocked, id=22148, stack(0x0000000e7dc00000,0x0000000e7dd00000)]
  0x000001fb5fd42000 JavaThread "Unconstrained build operations Thread 61" [_thread_blocked, id=2792, stack(0x0000000e7dd00000,0x0000000e7de00000)]
  0x000001fb5fd40800 JavaThread "Unconstrained build operations Thread 62" [_thread_blocked, id=5000, stack(0x0000000e7de00000,0x0000000e7df00000)]
  0x000001fb5fd3f800 JavaThread "Unconstrained build operations Thread 63" [_thread_blocked, id=23876, stack(0x0000000e7df00000,0x0000000e7e000000)]
  0x000001fb5fd45000 JavaThread "Unconstrained build operations Thread 64" [_thread_blocked, id=17068, stack(0x0000000e7e000000,0x0000000e7e100000)]
  0x000001fb5fd43000 JavaThread "Unconstrained build operations Thread 65" [_thread_blocked, id=26260, stack(0x0000000e7e100000,0x0000000e7e200000)]
  0x000001fb5fd46000 JavaThread "Unconstrained build operations Thread 66" [_thread_blocked, id=23628, stack(0x0000000e7e200000,0x0000000e7e300000)]
  0x000001fb5fd43800 JavaThread "Unconstrained build operations Thread 67" [_thread_blocked, id=7220, stack(0x0000000e7e300000,0x0000000e7e400000)]
  0x000001fb5fd44800 JavaThread "Unconstrained build operations Thread 68" [_thread_blocked, id=26572, stack(0x0000000e7e400000,0x0000000e7e500000)]
  0x000001fb5fd48800 JavaThread "Unconstrained build operations Thread 69" [_thread_blocked, id=25484, stack(0x0000000e7e500000,0x0000000e7e600000)]
  0x000001fb5fd49800 JavaThread "Unconstrained build operations Thread 70" [_thread_blocked, id=18700, stack(0x0000000e7e600000,0x0000000e7e700000)]
  0x000001fb5fd47800 JavaThread "Unconstrained build operations Thread 71" [_thread_blocked, id=19956, stack(0x0000000e7e700000,0x0000000e7e800000)]
  0x000001fb5fd47000 JavaThread "Unconstrained build operations Thread 72" [_thread_blocked, id=24712, stack(0x0000000e7e800000,0x0000000e7e900000)]
  0x000001fb5d589800 JavaThread "Unconstrained build operations Thread 73" [_thread_blocked, id=15440, stack(0x0000000e7e900000,0x0000000e7ea00000)]
  0x000001fb5d589000 JavaThread "Unconstrained build operations Thread 74" [_thread_blocked, id=24600, stack(0x0000000e7ea00000,0x0000000e7eb00000)]
  0x000001fb5d588000 JavaThread "Unconstrained build operations Thread 75" [_thread_blocked, id=9052, stack(0x0000000e7eb00000,0x0000000e7ec00000)]
  0x000001fb5d587000 JavaThread "Unconstrained build operations Thread 76" [_thread_blocked, id=4332, stack(0x0000000e7ec00000,0x0000000e7ed00000)]
  0x000001fb5d58b000 JavaThread "Unconstrained build operations Thread 77" [_thread_blocked, id=16508, stack(0x0000000e7ed00000,0x0000000e7ee00000)]
  0x000001fb5d58c000 JavaThread "jar transforms Thread 4" [_thread_blocked, id=4260, stack(0x0000000e7ee00000,0x0000000e7ef00000)]
  0x000001fb5d58a800 JavaThread "jar transforms Thread 5" [_thread_blocked, id=16580, stack(0x0000000e74e00000,0x0000000e74f00000)]
  0x000001fb5d58f800 JavaThread "Unconstrained build operations Thread 78" [_thread_blocked, id=22764, stack(0x0000000e76800000,0x0000000e76900000)]
  0x000001fb5d58d000 JavaThread "Unconstrained build operations Thread 79" [_thread_blocked, id=25208, stack(0x0000000e7a100000,0x0000000e7a200000)]
  0x000001fb5d590000 JavaThread "Unconstrained build operations Thread 80" [_thread_blocked, id=10800, stack(0x0000000e7ef00000,0x0000000e7f000000)]
  0x000001fb61982000 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=9800, stack(0x0000000e74d00000,0x0000000e74e00000)]
  0x000001fb6198b000 JavaThread "C2 CompilerThread2" daemon [_thread_blocked, id=8036, stack(0x0000000e74f00000,0x0000000e75000000)]

Other Threads:
=>0x000001fb5892e000 VMThread "VM Thread" [stack: 0x0000000e75600000,0x0000000e75700000] [id=26012]
  0x000001fb58e2c000 WatcherThread [stack: 0x0000000e76100000,0x0000000e76200000] [id=14868]
  0x000001fb3a070800 GCTaskThread "GC Thread#0" [stack: 0x0000000e75100000,0x0000000e75200000] [id=26356]
  0x000001fb59226000 GCTaskThread "GC Thread#1" [stack: 0x0000000e76300000,0x0000000e76400000] [id=16156]
  0x000001fb5932c800 GCTaskThread "GC Thread#2" [stack: 0x0000000e76400000,0x0000000e76500000] [id=24660]
  0x000001fb5932d000 GCTaskThread "GC Thread#3" [stack: 0x0000000e76500000,0x0000000e76600000] [id=19492]
  0x000001fb595ea000 GCTaskThread "GC Thread#4" [stack: 0x0000000e76600000,0x0000000e76700000] [id=20224]
  0x000001fb595ea800 GCTaskThread "GC Thread#5" [stack: 0x0000000e76700000,0x0000000e76800000] [id=24492]
  0x000001fb5b196800 GCTaskThread "GC Thread#6" [stack: 0x0000000e77100000,0x0000000e77200000] [id=20500]
  0x000001fb5b190000 GCTaskThread "GC Thread#7" [stack: 0x0000000e77200000,0x0000000e77300000] [id=23680]
  0x000001fb3a0a7000 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000000e75200000,0x0000000e75300000] [id=21724]
  0x000001fb3a0ae000 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000000e75300000,0x0000000e75400000] [id=5916]
  0x000001fb5a388000 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000000e77300000,0x0000000e77400000] [id=6332]
  0x000001fb3a11e000 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000000e75400000,0x0000000e75500000] [id=16936]
  0x000001fb5b539000 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000000e7a200000,0x0000000e7a300000] [id=19908]
  0x000001fb5a1e6000 ConcurrentGCThread "G1 Refine#2" [stack: 0x0000000e7a300000,0x0000000e7a400000] [id=8480]
  0x000001fb5e15f800 ConcurrentGCThread "G1 Refine#3" [stack: 0x0000000e7a400000,0x0000000e7a500000] [id=23904]
  0x000001fb60946800 ConcurrentGCThread "G1 Refine#4" [stack: 0x0000000e7a500000,0x0000000e7a600000] [id=23088]
  0x000001fb57fa0800 ConcurrentGCThread "G1 Young RemSet Sampling" [stack: 0x0000000e75500000,0x0000000e75600000] [id=12544]

Threads with active compile tasks:
C2 CompilerThread0    92666 21158       4       org.jetbrains.org.objectweb.asm.ClassReader::readElementValue (1237 bytes)
C2 CompilerThread1    92666 21186       4       org.jetbrains.org.objectweb.asm.ClassReader::readMethod (1061 bytes)
C2 CompilerThread2    92666 21159 %     4       org.jetbrains.org.objectweb.asm.ClassReader::accept @ 194 (1380 bytes)

VM state:at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001fb3a04ff80] Threads_lock - owner thread: 0x000001fb5892e000
[0x000001fb3a04f680] Heap_lock - owner thread: 0x000001fb5e071000

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 528482304 Address: 0x0000000100000000

Heap:
 garbage-first heap   total 448512K, used 142343K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 161804K, capacity 167202K, committed 167480K, reserved 663552K
  class space    used 19870K, capacity 21945K, committed 22004K, reserved 516096K
Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, A=archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%| O|  |TAMS 0x0000000080100000, 0x0000000080000000| Untracked 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%| O|  |TAMS 0x0000000080200000, 0x0000000080100000| Untracked 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HS|  |TAMS 0x0000000080300000, 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080400000, 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%|HC|  |TAMS 0x0000000080500000, 0x0000000080400000| Complete 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%|HS|  |TAMS 0x0000000080600000, 0x0000000080500000| Complete 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080700000, 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080800000, 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000, 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000, 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000, 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000, 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080d00000, 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000, 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080f00000, 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000, 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000, 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000, 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081300000, 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081400000, 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081500000, 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081600000, 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081700000, 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081800000, 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081900000, 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081a00000, 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081b00000, 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081c00000, 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081d00000, 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081e00000, 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081f00000, 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000082000000, 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%|HS|  |TAMS 0x0000000082100000, 0x0000000082000000| Complete 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%|HC|  |TAMS 0x0000000082200000, 0x0000000082100000| Complete 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%|HS|  |TAMS 0x0000000082300000, 0x0000000082200000| Complete 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%|HC|  |TAMS 0x0000000082400000, 0x0000000082300000| Complete 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%|HC|  |TAMS 0x0000000082500000, 0x0000000082400000| Complete 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%|HS|  |TAMS 0x0000000082600000, 0x0000000082500000| Complete 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%|HC|  |TAMS 0x0000000082700000, 0x0000000082600000| Complete 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%|HS|  |TAMS 0x0000000082800000, 0x0000000082700000| Complete 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%|HC|  |TAMS 0x0000000082900000, 0x0000000082800000| Complete 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%|HC|  |TAMS 0x0000000082a00000, 0x0000000082900000| Complete 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082b00000, 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082c00000, 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082d00000, 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%| O|  |TAMS 0x0000000082e00000, 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%| O|  |TAMS 0x0000000082f00000, 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%| O|  |TAMS 0x0000000083000000, 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%| O|  |TAMS 0x0000000083100000, 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%| O|  |TAMS 0x0000000083200000, 0x0000000083100000| Untracked 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%| O|  |TAMS 0x0000000083300000, 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%| O|  |TAMS 0x0000000083400000, 0x0000000083300000| Untracked 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%| O|  |TAMS 0x0000000083500000, 0x0000000083400000| Untracked 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%| O|  |TAMS 0x0000000083600000, 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%| O|  |TAMS 0x0000000083700000, 0x0000000083600000| Untracked 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%| O|  |TAMS 0x0000000083800000, 0x0000000083700000| Untracked 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%| O|  |TAMS 0x0000000083900000, 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%| O|  |TAMS 0x0000000083a00000, 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%| O|  |TAMS 0x0000000083b00000, 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%| O|  |TAMS 0x0000000083c00000, 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%| O|  |TAMS 0x0000000083d00000, 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083e00000, 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%| O|  |TAMS 0x0000000083f00000, 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000084000000, 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%| O|  |TAMS 0x0000000084100000, 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084200000, 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084300000, 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084400000, 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084500000, 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084600000, 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084700000, 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084800000, 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084900000, 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%| O|  |TAMS 0x0000000084a00000, 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084b00000, 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084c00000, 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084d00000, 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084e00000, 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084f00000, 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000085000000, 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085100000, 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085200000, 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085300000, 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085400000, 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085500000, 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%|HS|  |TAMS 0x0000000085500000, 0x0000000085500000| Complete 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085600000, 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085800000, 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085900000, 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085a00000, 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085a00000, 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085c00000, 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%|HS|  |TAMS 0x0000000085c00000, 0x0000000085c00000| Complete 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085e00000, 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085f00000, 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000086000000, 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086100000, 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086200000, 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086300000, 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086400000, 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086500000, 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086600000, 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086700000, 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086800000, 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086900000, 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086a00000, 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086b00000, 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086c00000, 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086d00000, 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086e00000, 0x0000000086d00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086f00000, 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000087000000, 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087100000, 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087200000, 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087300000, 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| O|  |TAMS 0x0000000087400000, 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%| O|  |TAMS 0x0000000087500000, 0x0000000087400000| Untracked 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087600000, 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087700000, 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087800000, 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| O|  |TAMS 0x0000000087900000, 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087933c00, 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| O|  |TAMS 0x0000000087a00000, 0x0000000087a00000| Untracked 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| O|  |TAMS 0x0000000087b00000, 0x0000000087b00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| O|  |TAMS 0x0000000087c00000, 0x0000000087c00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| O|  |TAMS 0x0000000087d00000, 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087e00000, 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| O|  |TAMS 0x0000000087f00000, 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x00000000880b1200, 0x0000000088100000| 69%| O|  |TAMS 0x0000000088000000, 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088100000, 0x0000000088200000|  0%| F|  |TAMS 0x0000000088100000, 0x0000000088100000| Untracked 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%| O|  |TAMS 0x0000000088300000, 0x0000000088200000| Untracked 
| 131|0x0000000088300000, 0x0000000088300000, 0x0000000088400000|  0%| F|  |TAMS 0x0000000088300000, 0x0000000088300000| Untracked 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%| O|  |TAMS 0x0000000088500000, 0x0000000088400000| Untracked 
| 133|0x0000000088500000, 0x0000000088500000, 0x0000000088600000|  0%| F|  |TAMS 0x0000000088500000, 0x0000000088500000| Untracked 
| 134|0x0000000088600000, 0x0000000088600000, 0x0000000088700000|  0%| F|  |TAMS 0x0000000088600000, 0x0000000088600000| Untracked 
| 135|0x0000000088700000, 0x0000000088700000, 0x0000000088800000|  0%| F|  |TAMS 0x0000000088700000, 0x0000000088700000| Untracked 
| 136|0x0000000088800000, 0x0000000088800000, 0x0000000088900000|  0%| F|  |TAMS 0x0000000088800000, 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088900000, 0x0000000088a00000|  0%| F|  |TAMS 0x0000000088900000, 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088a00000, 0x0000000088b00000|  0%| F|  |TAMS 0x0000000088a00000, 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088b00000, 0x0000000088c00000|  0%| F|  |TAMS 0x0000000088b00000, 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088c00000, 0x0000000088d00000|  0%| F|  |TAMS 0x0000000088c00000, 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088d00000, 0x0000000088e00000|  0%| F|  |TAMS 0x0000000088d00000, 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088e00000, 0x0000000088f00000|  0%| F|  |TAMS 0x0000000088e00000, 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000088f00000, 0x0000000089000000|  0%| F|  |TAMS 0x0000000088f00000, 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089000000, 0x0000000089100000|  0%| F|  |TAMS 0x0000000089000000, 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089100000, 0x0000000089200000|  0%| F|  |TAMS 0x0000000089100000, 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089200000, 0x0000000089300000|  0%| F|  |TAMS 0x0000000089200000, 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089300000, 0x0000000089400000|  0%| F|  |TAMS 0x0000000089300000, 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089400000, 0x0000000089500000|  0%| F|  |TAMS 0x0000000089400000, 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089500000, 0x0000000089600000|  0%| F|  |TAMS 0x0000000089500000, 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x0000000089600000, 0x0000000089700000|  0%| F|  |TAMS 0x0000000089600000, 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089700000, 0x0000000089800000|  0%| F|  |TAMS 0x0000000089700000, 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089800000, 0x0000000089900000|  0%| F|  |TAMS 0x0000000089800000, 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089900000, 0x0000000089a00000|  0%| F|  |TAMS 0x0000000089900000, 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089a00000, 0x0000000089b00000|  0%| F|  |TAMS 0x0000000089a00000, 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089b00000, 0x0000000089c00000|  0%| F|  |TAMS 0x0000000089b00000, 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089c00000, 0x0000000089d00000|  0%| F|  |TAMS 0x0000000089c00000, 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089d00000, 0x0000000089e00000|  0%| F|  |TAMS 0x0000000089d00000, 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089e00000, 0x0000000089f00000|  0%| F|  |TAMS 0x0000000089e00000, 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x0000000089f00000, 0x000000008a000000|  0%| F|  |TAMS 0x0000000089f00000, 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a000000, 0x000000008a100000|  0%| F|  |TAMS 0x000000008a000000, 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a100000, 0x000000008a200000|  0%| F|  |TAMS 0x000000008a100000, 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a200000, 0x000000008a300000|  0%| F|  |TAMS 0x000000008a200000, 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a300000, 0x000000008a400000|  0%| F|  |TAMS 0x000000008a300000, 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a400000, 0x000000008a500000|  0%| F|  |TAMS 0x000000008a400000, 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a500000, 0x000000008a600000|  0%| F|  |TAMS 0x000000008a500000, 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a600000, 0x000000008a700000|  0%| F|  |TAMS 0x000000008a600000, 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a700000, 0x000000008a800000|  0%| F|  |TAMS 0x000000008a700000, 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a800000, 0x000000008a900000|  0%| F|  |TAMS 0x000000008a800000, 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008a900000, 0x000000008aa00000|  0%| F|  |TAMS 0x000000008a900000, 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008aa00000, 0x000000008ab00000|  0%| F|  |TAMS 0x000000008aa00000, 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ab00000, 0x000000008ac00000|  0%| F|  |TAMS 0x000000008ab00000, 0x000000008ab00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ac00000, 0x000000008ad00000|  0%| F|  |TAMS 0x000000008ac00000, 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ad00000, 0x000000008ae00000|  0%| F|  |TAMS 0x000000008ad00000, 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008ae00000, 0x000000008af00000|  0%| F|  |TAMS 0x000000008ae00000, 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008af00000, 0x000000008b000000|  0%| F|  |TAMS 0x000000008af00000, 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b000000, 0x000000008b100000|  0%| F|  |TAMS 0x000000008b000000, 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b100000, 0x000000008b200000|  0%| F|  |TAMS 0x000000008b100000, 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b200000, 0x000000008b300000|  0%| F|  |TAMS 0x000000008b200000, 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b300000, 0x000000008b400000|  0%| F|  |TAMS 0x000000008b300000, 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b400000, 0x000000008b500000|  0%| F|  |TAMS 0x000000008b400000, 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b500000, 0x000000008b600000|  0%| F|  |TAMS 0x000000008b500000, 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b600000, 0x000000008b700000|  0%| F|  |TAMS 0x000000008b600000, 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b700000, 0x000000008b800000|  0%| F|  |TAMS 0x000000008b700000, 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b800000, 0x000000008b900000|  0%| F|  |TAMS 0x000000008b800000, 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008b900000, 0x000000008ba00000|  0%| F|  |TAMS 0x000000008b900000, 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008ba00000, 0x000000008bb00000|  0%| F|  |TAMS 0x000000008ba00000, 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bb00000, 0x000000008bc00000|  0%| F|  |TAMS 0x000000008bb00000, 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bc50c50, 0x000000008bd00000| 31%| S|CS|TAMS 0x000000008bc00000, 0x000000008bc00000| Complete 
| 189|0x000000008bd00000, 0x000000008be00000, 0x000000008be00000|100%| S|CS|TAMS 0x000000008bd00000, 0x000000008bd00000| Complete 
| 190|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%| S|CS|TAMS 0x000000008be00000, 0x000000008be00000| Complete 
| 191|0x000000008bf00000, 0x000000008c000000, 0x000000008c000000|100%| S|CS|TAMS 0x000000008bf00000, 0x000000008bf00000| Complete 
| 192|0x000000008c000000, 0x000000008c100000, 0x000000008c100000|100%| S|CS|TAMS 0x000000008c000000, 0x000000008c000000| Complete 
| 193|0x000000008c100000, 0x000000008c200000, 0x000000008c200000|100%| S|CS|TAMS 0x000000008c100000, 0x000000008c100000| Complete 
| 194|0x000000008c200000, 0x000000008c200000, 0x000000008c300000|  0%| F|  |TAMS 0x000000008c200000, 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c300000, 0x000000008c400000|  0%| F|  |TAMS 0x000000008c300000, 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c400000, 0x000000008c500000|  0%| F|  |TAMS 0x000000008c400000, 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c500000, 0x000000008c600000|  0%| F|  |TAMS 0x000000008c500000, 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c600000, 0x000000008c700000|  0%| F|  |TAMS 0x000000008c600000, 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c700000, 0x000000008c800000|  0%| F|  |TAMS 0x000000008c700000, 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%| S|CS|TAMS 0x000000008c800000, 0x000000008c800000| Complete 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%| S|CS|TAMS 0x000000008c900000, 0x000000008c900000| Complete 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%| S|CS|TAMS 0x000000008ca00000, 0x000000008ca00000| Complete 
| 203|0x000000008cb00000, 0x000000008cb00000, 0x000000008cc00000|  0%| F|  |TAMS 0x000000008cb00000, 0x000000008cb00000| Untracked 
| 204|0x000000008cc00000, 0x000000008cc00000, 0x000000008cd00000|  0%| F|  |TAMS 0x000000008cc00000, 0x000000008cc00000| Untracked 
| 205|0x000000008cd00000, 0x000000008cd00000, 0x000000008ce00000|  0%| F|  |TAMS 0x000000008cd00000, 0x000000008cd00000| Untracked 
| 206|0x000000008ce00000, 0x000000008ce00000, 0x000000008cf00000|  0%| F|  |TAMS 0x000000008ce00000, 0x000000008ce00000| Untracked 
| 207|0x000000008cf00000, 0x000000008cf00000, 0x000000008d000000|  0%| F|  |TAMS 0x000000008cf00000, 0x000000008cf00000| Untracked 
| 208|0x000000008d000000, 0x000000008d000000, 0x000000008d100000|  0%| F|  |TAMS 0x000000008d000000, 0x000000008d000000| Untracked 
| 209|0x000000008d100000, 0x000000008d100000, 0x000000008d200000|  0%| F|  |TAMS 0x000000008d100000, 0x000000008d100000| Untracked 
| 210|0x000000008d200000, 0x000000008d200000, 0x000000008d300000|  0%| F|  |TAMS 0x000000008d200000, 0x000000008d200000| Untracked 
| 211|0x000000008d300000, 0x000000008d300000, 0x000000008d400000|  0%| F|  |TAMS 0x000000008d300000, 0x000000008d300000| Untracked 
| 212|0x000000008d400000, 0x000000008d400000, 0x000000008d500000|  0%| F|  |TAMS 0x000000008d400000, 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d500000, 0x000000008d600000|  0%| F|  |TAMS 0x000000008d500000, 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d600000, 0x000000008d700000|  0%| F|  |TAMS 0x000000008d600000, 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d700000, 0x000000008d800000|  0%| F|  |TAMS 0x000000008d700000, 0x000000008d700000| Untracked 
| 216|0x000000008d800000, 0x000000008d800000, 0x000000008d900000|  0%| F|  |TAMS 0x000000008d800000, 0x000000008d800000| Untracked 
| 217|0x000000008d900000, 0x000000008d900000, 0x000000008da00000|  0%| F|  |TAMS 0x000000008d900000, 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008da00000, 0x000000008db00000|  0%| F|  |TAMS 0x000000008da00000, 0x000000008da00000| Untracked 
| 219|0x000000008db00000, 0x000000008db00000, 0x000000008dc00000|  0%| F|  |TAMS 0x000000008db00000, 0x000000008db00000| Untracked 
| 220|0x000000008dc00000, 0x000000008dc00000, 0x000000008dd00000|  0%| F|  |TAMS 0x000000008dc00000, 0x000000008dc00000| Untracked 
| 221|0x000000008dd00000, 0x000000008dd00000, 0x000000008de00000|  0%| F|  |TAMS 0x000000008dd00000, 0x000000008dd00000| Untracked 
| 222|0x000000008de00000, 0x000000008de00000, 0x000000008df00000|  0%| F|  |TAMS 0x000000008de00000, 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008df00000, 0x000000008e000000|  0%| F|  |TAMS 0x000000008df00000, 0x000000008df00000| Untracked 
| 224|0x000000008e000000, 0x000000008e000000, 0x000000008e100000|  0%| F|  |TAMS 0x000000008e000000, 0x000000008e000000| Untracked 
| 225|0x000000008e100000, 0x000000008e100000, 0x000000008e200000|  0%| F|  |TAMS 0x000000008e100000, 0x000000008e100000| Untracked 
| 226|0x000000008e200000, 0x000000008e200000, 0x000000008e300000|  0%| F|  |TAMS 0x000000008e200000, 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e300000, 0x000000008e400000|  0%| F|  |TAMS 0x000000008e300000, 0x000000008e300000| Untracked 
| 228|0x000000008e400000, 0x000000008e400000, 0x000000008e500000|  0%| F|  |TAMS 0x000000008e400000, 0x000000008e400000| Untracked 
| 229|0x000000008e500000, 0x000000008e500000, 0x000000008e600000|  0%| F|  |TAMS 0x000000008e500000, 0x000000008e500000| Untracked 
| 230|0x000000008e600000, 0x000000008e600000, 0x000000008e700000|  0%| F|  |TAMS 0x000000008e600000, 0x000000008e600000| Untracked 
| 231|0x000000008e700000, 0x000000008e700000, 0x000000008e800000|  0%| F|  |TAMS 0x000000008e700000, 0x000000008e700000| Untracked 
| 232|0x000000008e800000, 0x000000008e800000, 0x000000008e900000|  0%| F|  |TAMS 0x000000008e800000, 0x000000008e800000| Untracked 
| 233|0x000000008e900000, 0x000000008e900000, 0x000000008ea00000|  0%| F|  |TAMS 0x000000008e900000, 0x000000008e900000| Untracked 
| 234|0x000000008ea00000, 0x000000008ea00000, 0x000000008eb00000|  0%| F|  |TAMS 0x000000008ea00000, 0x000000008ea00000| Untracked 
| 235|0x000000008eb00000, 0x000000008eb00000, 0x000000008ec00000|  0%| F|  |TAMS 0x000000008eb00000, 0x000000008eb00000| Untracked 
| 236|0x000000008ec00000, 0x000000008ec00000, 0x000000008ed00000|  0%| F|  |TAMS 0x000000008ec00000, 0x000000008ec00000| Untracked 
| 237|0x000000008ed00000, 0x000000008ed00000, 0x000000008ee00000|  0%| F|  |TAMS 0x000000008ed00000, 0x000000008ed00000| Untracked 
| 238|0x000000008ee00000, 0x000000008ee00000, 0x000000008ef00000|  0%| F|  |TAMS 0x000000008ee00000, 0x000000008ee00000| Untracked 
| 239|0x000000008ef00000, 0x000000008ef00000, 0x000000008f000000|  0%| F|  |TAMS 0x000000008ef00000, 0x000000008ef00000| Untracked 
| 240|0x000000008f000000, 0x000000008f000000, 0x000000008f100000|  0%| F|  |TAMS 0x000000008f000000, 0x000000008f000000| Untracked 
| 241|0x000000008f100000, 0x000000008f100000, 0x000000008f200000|  0%| F|  |TAMS 0x000000008f100000, 0x000000008f100000| Untracked 
| 242|0x000000008f200000, 0x000000008f200000, 0x000000008f300000|  0%| F|  |TAMS 0x000000008f200000, 0x000000008f200000| Untracked 
| 243|0x000000008f300000, 0x000000008f300000, 0x000000008f400000|  0%| F|  |TAMS 0x000000008f300000, 0x000000008f300000| Untracked 
| 244|0x000000008f400000, 0x000000008f400000, 0x000000008f500000|  0%| F|  |TAMS 0x000000008f400000, 0x000000008f400000| Untracked 
| 245|0x000000008f500000, 0x000000008f500000, 0x000000008f600000|  0%| F|  |TAMS 0x000000008f500000, 0x000000008f500000| Untracked 
| 246|0x000000008f600000, 0x000000008f600000, 0x000000008f700000|  0%| F|  |TAMS 0x000000008f600000, 0x000000008f600000| Untracked 
| 247|0x000000008f700000, 0x000000008f700000, 0x000000008f800000|  0%| F|  |TAMS 0x000000008f700000, 0x000000008f700000| Untracked 
| 248|0x000000008f800000, 0x000000008f800000, 0x000000008f900000|  0%| F|  |TAMS 0x000000008f800000, 0x000000008f800000| Untracked 
| 249|0x000000008f900000, 0x000000008f900000, 0x000000008fa00000|  0%| F|  |TAMS 0x000000008f900000, 0x000000008f900000| Untracked 
| 250|0x000000008fa00000, 0x000000008fa00000, 0x000000008fb00000|  0%| F|  |TAMS 0x000000008fa00000, 0x000000008fa00000| Untracked 
| 251|0x000000008fb00000, 0x000000008fb00000, 0x000000008fc00000|  0%| F|  |TAMS 0x000000008fb00000, 0x000000008fb00000| Untracked 
| 252|0x000000008fc00000, 0x000000008fc00000, 0x000000008fd00000|  0%| F|  |TAMS 0x000000008fc00000, 0x000000008fc00000| Untracked 
| 253|0x000000008fd00000, 0x000000008fd00000, 0x000000008fe00000|  0%| F|  |TAMS 0x000000008fd00000, 0x000000008fd00000| Untracked 
| 254|0x000000008fe00000, 0x000000008fe00000, 0x000000008ff00000|  0%| F|  |TAMS 0x000000008fe00000, 0x000000008fe00000| Untracked 
| 255|0x000000008ff00000, 0x000000008ff00000, 0x0000000090000000|  0%| F|  |TAMS 0x000000008ff00000, 0x000000008ff00000| Untracked 
| 256|0x0000000090000000, 0x0000000090000000, 0x0000000090100000|  0%| F|  |TAMS 0x0000000090000000, 0x0000000090000000| Untracked 
| 257|0x0000000090100000, 0x0000000090100000, 0x0000000090200000|  0%| F|  |TAMS 0x0000000090100000, 0x0000000090100000| Untracked 
| 258|0x0000000090200000, 0x0000000090200000, 0x0000000090300000|  0%| F|  |TAMS 0x0000000090200000, 0x0000000090200000| Untracked 
| 259|0x0000000090300000, 0x0000000090300000, 0x0000000090400000|  0%| F|  |TAMS 0x0000000090300000, 0x0000000090300000| Untracked 
| 260|0x0000000090400000, 0x0000000090400000, 0x0000000090500000|  0%| F|  |TAMS 0x0000000090400000, 0x0000000090400000| Untracked 
| 261|0x0000000090500000, 0x0000000090500000, 0x0000000090600000|  0%| F|  |TAMS 0x0000000090500000, 0x0000000090500000| Untracked 
| 262|0x0000000090600000, 0x0000000090600000, 0x0000000090700000|  0%| F|  |TAMS 0x0000000090600000, 0x0000000090600000| Untracked 
| 263|0x0000000090700000, 0x0000000090700000, 0x0000000090800000|  0%| F|  |TAMS 0x0000000090700000, 0x0000000090700000| Untracked 
| 264|0x0000000090800000, 0x0000000090800000, 0x0000000090900000|  0%| F|  |TAMS 0x0000000090800000, 0x0000000090800000| Untracked 
| 265|0x0000000090900000, 0x0000000090900000, 0x0000000090a00000|  0%| F|  |TAMS 0x0000000090900000, 0x0000000090900000| Untracked 
| 266|0x0000000090a00000, 0x0000000090a00000, 0x0000000090b00000|  0%| F|  |TAMS 0x0000000090a00000, 0x0000000090a00000| Untracked 
| 267|0x0000000090b00000, 0x0000000090b00000, 0x0000000090c00000|  0%| F|  |TAMS 0x0000000090b00000, 0x0000000090b00000| Untracked 
| 268|0x0000000090c00000, 0x0000000090c00000, 0x0000000090d00000|  0%| F|  |TAMS 0x0000000090c00000, 0x0000000090c00000| Untracked 
| 269|0x0000000090d00000, 0x0000000090d00000, 0x0000000090e00000|  0%| F|  |TAMS 0x0000000090d00000, 0x0000000090d00000| Untracked 
| 270|0x0000000090e00000, 0x0000000090e00000, 0x0000000090f00000|  0%| F|  |TAMS 0x0000000090e00000, 0x0000000090e00000| Untracked 
| 271|0x0000000090f00000, 0x0000000090f00000, 0x0000000091000000|  0%| F|  |TAMS 0x0000000090f00000, 0x0000000090f00000| Untracked 
| 272|0x0000000091000000, 0x0000000091000000, 0x0000000091100000|  0%| F|  |TAMS 0x0000000091000000, 0x0000000091000000| Untracked 
| 273|0x0000000091100000, 0x0000000091100000, 0x0000000091200000|  0%| F|  |TAMS 0x0000000091100000, 0x0000000091100000| Untracked 
| 274|0x0000000091200000, 0x0000000091200000, 0x0000000091300000|  0%| F|  |TAMS 0x0000000091200000, 0x0000000091200000| Untracked 
| 275|0x0000000091300000, 0x0000000091300000, 0x0000000091400000|  0%| F|  |TAMS 0x0000000091300000, 0x0000000091300000| Untracked 
| 276|0x0000000091400000, 0x0000000091400000, 0x0000000091500000|  0%| F|  |TAMS 0x0000000091400000, 0x0000000091400000| Untracked 
| 277|0x0000000091500000, 0x0000000091500000, 0x0000000091600000|  0%| F|  |TAMS 0x0000000091500000, 0x0000000091500000| Untracked 
| 278|0x0000000091600000, 0x0000000091600000, 0x0000000091700000|  0%| F|  |TAMS 0x0000000091600000, 0x0000000091600000| Untracked 
| 279|0x0000000091700000, 0x0000000091700000, 0x0000000091800000|  0%| F|  |TAMS 0x0000000091700000, 0x0000000091700000| Untracked 
| 280|0x0000000091800000, 0x0000000091800000, 0x0000000091900000|  0%| F|  |TAMS 0x0000000091800000, 0x0000000091800000| Untracked 
| 281|0x0000000091900000, 0x0000000091900000, 0x0000000091a00000|  0%| F|  |TAMS 0x0000000091900000, 0x0000000091900000| Untracked 
| 282|0x0000000091a00000, 0x0000000091a00000, 0x0000000091b00000|  0%| F|  |TAMS 0x0000000091a00000, 0x0000000091a00000| Untracked 
| 283|0x0000000091b00000, 0x0000000091b00000, 0x0000000091c00000|  0%| F|  |TAMS 0x0000000091b00000, 0x0000000091b00000| Untracked 
| 284|0x0000000091c00000, 0x0000000091c00000, 0x0000000091d00000|  0%| F|  |TAMS 0x0000000091c00000, 0x0000000091c00000| Untracked 
| 285|0x0000000091d00000, 0x0000000091d00000, 0x0000000091e00000|  0%| F|  |TAMS 0x0000000091d00000, 0x0000000091d00000| Untracked 
| 286|0x0000000091e00000, 0x0000000091e00000, 0x0000000091f00000|  0%| F|  |TAMS 0x0000000091e00000, 0x0000000091e00000| Untracked 
| 287|0x0000000091f00000, 0x0000000091f00000, 0x0000000092000000|  0%| F|  |TAMS 0x0000000091f00000, 0x0000000091f00000| Untracked 
| 288|0x0000000092000000, 0x0000000092000000, 0x0000000092100000|  0%| F|  |TAMS 0x0000000092000000, 0x0000000092000000| Untracked 
| 289|0x0000000092100000, 0x0000000092100000, 0x0000000092200000|  0%| F|  |TAMS 0x0000000092100000, 0x0000000092100000| Untracked 
| 290|0x0000000092200000, 0x0000000092200000, 0x0000000092300000|  0%| F|  |TAMS 0x0000000092200000, 0x0000000092200000| Untracked 
| 291|0x0000000092300000, 0x0000000092300000, 0x0000000092400000|  0%| F|  |TAMS 0x0000000092300000, 0x0000000092300000| Untracked 
| 292|0x0000000092400000, 0x0000000092400000, 0x0000000092500000|  0%| F|  |TAMS 0x0000000092400000, 0x0000000092400000| Untracked 
| 293|0x0000000092500000, 0x0000000092500000, 0x0000000092600000|  0%| F|  |TAMS 0x0000000092500000, 0x0000000092500000| Untracked 
| 294|0x0000000092600000, 0x0000000092600000, 0x0000000092700000|  0%| F|  |TAMS 0x0000000092600000, 0x0000000092600000| Untracked 
| 295|0x0000000092700000, 0x0000000092700000, 0x0000000092800000|  0%| F|  |TAMS 0x0000000092700000, 0x0000000092700000| Untracked 
| 296|0x0000000092800000, 0x0000000092800000, 0x0000000092900000|  0%| F|  |TAMS 0x0000000092800000, 0x0000000092800000| Untracked 
| 297|0x0000000092900000, 0x0000000092900000, 0x0000000092a00000|  0%| F|  |TAMS 0x0000000092900000, 0x0000000092900000| Untracked 
| 298|0x0000000092a00000, 0x0000000092a00000, 0x0000000092b00000|  0%| F|  |TAMS 0x0000000092a00000, 0x0000000092a00000| Untracked 
| 299|0x0000000092b00000, 0x0000000092b00000, 0x0000000092c00000|  0%| F|  |TAMS 0x0000000092b00000, 0x0000000092b00000| Untracked 
| 300|0x0000000092c00000, 0x0000000092c00000, 0x0000000092d00000|  0%| F|  |TAMS 0x0000000092c00000, 0x0000000092c00000| Untracked 
| 301|0x0000000092d00000, 0x0000000092d00000, 0x0000000092e00000|  0%| F|  |TAMS 0x0000000092d00000, 0x0000000092d00000| Untracked 
| 302|0x0000000092e00000, 0x0000000092e00000, 0x0000000092f00000|  0%| F|  |TAMS 0x0000000092e00000, 0x0000000092e00000| Untracked 
| 303|0x0000000092f00000, 0x0000000092f00000, 0x0000000093000000|  0%| F|  |TAMS 0x0000000092f00000, 0x0000000092f00000| Untracked 
| 304|0x0000000093000000, 0x0000000093000000, 0x0000000093100000|  0%| F|  |TAMS 0x0000000093000000, 0x0000000093000000| Untracked 
| 305|0x0000000093100000, 0x0000000093100000, 0x0000000093200000|  0%| F|  |TAMS 0x0000000093100000, 0x0000000093100000| Untracked 
| 306|0x0000000093200000, 0x0000000093200000, 0x0000000093300000|  0%| F|  |TAMS 0x0000000093200000, 0x0000000093200000| Untracked 
| 307|0x0000000093300000, 0x0000000093300000, 0x0000000093400000|  0%| F|  |TAMS 0x0000000093300000, 0x0000000093300000| Untracked 
| 308|0x0000000093400000, 0x0000000093400000, 0x0000000093500000|  0%| F|  |TAMS 0x0000000093400000, 0x0000000093400000| Untracked 
| 309|0x0000000093500000, 0x0000000093500000, 0x0000000093600000|  0%| F|  |TAMS 0x0000000093500000, 0x0000000093500000| Untracked 
| 310|0x0000000093600000, 0x0000000093600000, 0x0000000093700000|  0%| F|  |TAMS 0x0000000093600000, 0x0000000093600000| Untracked 
| 311|0x0000000093700000, 0x0000000093700000, 0x0000000093800000|  0%| F|  |TAMS 0x0000000093700000, 0x0000000093700000| Untracked 
| 312|0x0000000093800000, 0x0000000093800000, 0x0000000093900000|  0%| F|  |TAMS 0x0000000093800000, 0x0000000093800000| Untracked 
| 313|0x0000000093900000, 0x0000000093900000, 0x0000000093a00000|  0%| F|  |TAMS 0x0000000093900000, 0x0000000093900000| Untracked 
| 314|0x0000000093a00000, 0x0000000093a00000, 0x0000000093b00000|  0%| F|  |TAMS 0x0000000093a00000, 0x0000000093a00000| Untracked 
| 315|0x0000000093b00000, 0x0000000093b00000, 0x0000000093c00000|  0%| F|  |TAMS 0x0000000093b00000, 0x0000000093b00000| Untracked 
| 316|0x0000000093c00000, 0x0000000093c00000, 0x0000000093d00000|  0%| F|  |TAMS 0x0000000093c00000, 0x0000000093c00000| Untracked 
| 317|0x0000000093d00000, 0x0000000093d00000, 0x0000000093e00000|  0%| F|  |TAMS 0x0000000093d00000, 0x0000000093d00000| Untracked 
| 318|0x0000000093e00000, 0x0000000093e00000, 0x0000000093f00000|  0%| F|  |TAMS 0x0000000093e00000, 0x0000000093e00000| Untracked 
| 319|0x0000000093f00000, 0x0000000093f00000, 0x0000000094000000|  0%| F|  |TAMS 0x0000000093f00000, 0x0000000093f00000| Untracked 
| 320|0x0000000094000000, 0x0000000094000000, 0x0000000094100000|  0%| F|  |TAMS 0x0000000094000000, 0x0000000094000000| Untracked 
| 321|0x0000000094100000, 0x0000000094100000, 0x0000000094200000|  0%| F|  |TAMS 0x0000000094100000, 0x0000000094100000| Untracked 
| 322|0x0000000094200000, 0x0000000094200000, 0x0000000094300000|  0%| F|  |TAMS 0x0000000094200000, 0x0000000094200000| Untracked 
| 323|0x0000000094300000, 0x0000000094300000, 0x0000000094400000|  0%| F|  |TAMS 0x0000000094300000, 0x0000000094300000| Untracked 
| 324|0x0000000094400000, 0x0000000094400000, 0x0000000094500000|  0%| F|  |TAMS 0x0000000094400000, 0x0000000094400000| Untracked 
| 325|0x0000000094500000, 0x0000000094500000, 0x0000000094600000|  0%| F|  |TAMS 0x0000000094500000, 0x0000000094500000| Untracked 
| 326|0x0000000094600000, 0x0000000094600000, 0x0000000094700000|  0%| F|  |TAMS 0x0000000094600000, 0x0000000094600000| Untracked 
| 327|0x0000000094700000, 0x0000000094700000, 0x0000000094800000|  0%| F|  |TAMS 0x0000000094700000, 0x0000000094700000| Untracked 
| 328|0x0000000094800000, 0x0000000094800000, 0x0000000094900000|  0%| F|  |TAMS 0x0000000094800000, 0x0000000094800000| Untracked 
| 329|0x0000000094900000, 0x0000000094900000, 0x0000000094a00000|  0%| F|  |TAMS 0x0000000094900000, 0x0000000094900000| Untracked 
| 330|0x0000000094a00000, 0x0000000094a00000, 0x0000000094b00000|  0%| F|  |TAMS 0x0000000094a00000, 0x0000000094a00000| Untracked 
| 331|0x0000000094b00000, 0x0000000094b00000, 0x0000000094c00000|  0%| F|  |TAMS 0x0000000094b00000, 0x0000000094b00000| Untracked 

Card table byte_map: [0x000001fb50e80000,0x000001fb51280000] _byte_map_base: 0x000001fb50a80000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001fb3a09cc38, (CMBitMap*) 0x000001fb3a09cc70
 Prev Bits: [0x000001fb51680000, 0x000001fb53680000)
 Next Bits: [0x000001fb53680000, 0x000001fb55680000)

Polling page: 0x000001fb37f90000

Metaspace:

Usage:
  Non-class:    141.85 MB capacity,   138.61 MB ( 98%) used,     2.66 MB (  2%) free+waste,   596.69 KB ( <1%) overhead. 
      Class:     21.43 MB capacity,    19.41 MB ( 91%) used,     1.75 MB (  8%) free+waste,   279.81 KB (  1%) overhead. 
       Both:    163.28 MB capacity,   158.01 MB ( 97%) used,     4.42 MB (  3%) free+waste,   876.50 KB ( <1%) overhead. 

Virtual space:
  Non-class space:      144.00 MB reserved,     142.07 MB ( 99%) committed 
      Class space:      504.00 MB reserved,      21.49 MB (  4%) committed 
             Both:      648.00 MB reserved,     163.55 MB ( 25%) committed 

Chunk freelists:
   Non-Class:  3.38 KB
       Class:  4.88 KB
        Both:  8.25 KB

CodeHeap 'non-profiled nmethods': size=120000Kb used=11806Kb max_used=11806Kb free=108193Kb
 bounds [0x000001fb49220000, 0x000001fb49db0000, 0x000001fb50750000]
CodeHeap 'profiled nmethods': size=120000Kb used=48566Kb max_used=48566Kb free=71433Kb
 bounds [0x000001fb41cf0000, 0x000001fb44c70000, 0x000001fb49220000]
CodeHeap 'non-nmethods': size=5760Kb used=2476Kb max_used=2536Kb free=3283Kb
 bounds [0x000001fb41750000, 0x000001fb419e0000, 0x000001fb41cf0000]
 total_blobs=20635 nmethods=19481 adapters=1061
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (10 events):
Event: 91.376 Thread 0x000001fb589b3000 21199       2       org.jetbrains.org.objectweb.asm.tree.LdcInsnNode::<init> (12 bytes)
Event: 91.376 Thread 0x000001fb589b3000 nmethod 21199 0x000001fb44c58710 code [0x000001fb44c588a0, 0x000001fb44c589f8]
Event: 91.376 Thread 0x000001fb589b3000 21200       2       org.jetbrains.org.objectweb.asm.tree.LdcInsnNode::accept (14 bytes)
Event: 91.377 Thread 0x000001fb589b3000 nmethod 21200 0x000001fb44c58a90 code [0x000001fb44c58c40, 0x000001fb44c58d98]
Event: 91.406 Thread 0x000001fb589b3000 21201       2       org.jetbrains.org.objectweb.asm.Handler::getExceptionTableLength (21 bytes)
Event: 91.406 Thread 0x000001fb589b3000 nmethod 21201 0x000001fb44c58e90 code [0x000001fb44c59020, 0x000001fb44c59158]
Event: 91.421 Thread 0x000001fb589b3000 21202       2       org.jetbrains.org.objectweb.asm.ClassReader::readCode (5089 bytes)
Event: 91.462 Thread 0x000001fb589b3000 nmethod 21202 0x000001fb44c59290 code [0x000001fb44c59de0, 0x000001fb44c62388]
Event: 91.462 Thread 0x000001fb589b3000 21203       2       org.jetbrains.org.objectweb.asm.MethodWriter::visitTypeInsn (116 bytes)
Event: 91.463 Thread 0x000001fb589b3000 nmethod 21203 0x000001fb44c6a010 code [0x000001fb44c6a1e0, 0x000001fb44c6a428]

GC Heap History (10 events):
Event: 90.104 GC heap after
{Heap after GC invocations=53 (full 0):
 garbage-first heap   total 260096K, used 137894K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 161725K, capacity 167138K, committed 167224K, reserved 661504K
  class space    used 19868K, capacity 21945K, committed 22004K, reserved 516096K
}
Event: 90.290 GC heap before
{Heap before GC invocations=53 (full 0):
 garbage-first heap   total 260096K, used 220838K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 86 young (88064K), 4 survivors (4096K)
 Metaspace       used 161778K, capacity 167202K, committed 167480K, reserved 663552K
  class space    used 19870K, capacity 21945K, committed 22004K, reserved 516096K
}
Event: 90.301 GC heap after
{Heap after GC invocations=54 (full 0):
 garbage-first heap   total 339968K, used 138385K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 5 survivors (5120K)
 Metaspace       used 161778K, capacity 167202K, committed 167480K, reserved 663552K
  class space    used 19870K, capacity 21945K, committed 22004K, reserved 516096K
}
Event: 90.553 GC heap before
{Heap before GC invocations=54 (full 0):
 garbage-first heap   total 339968K, used 256145K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 120 young (122880K), 5 survivors (5120K)
 Metaspace       used 161795K, capacity 167202K, committed 167480K, reserved 663552K
  class space    used 19870K, capacity 21945K, committed 22004K, reserved 516096K
}
Event: 90.563 GC heap after
{Heap after GC invocations=55 (full 0):
 garbage-first heap   total 339968K, used 139352K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 161795K, capacity 167202K, committed 167480K, reserved 663552K
  class space    used 19870K, capacity 21945K, committed 22004K, reserved 516096K
}
Event: 90.813 GC heap before
{Heap before GC invocations=55 (full 0):
 garbage-first heap   total 339968K, used 261208K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 125 young (128000K), 6 survivors (6144K)
 Metaspace       used 161795K, capacity 167202K, committed 167480K, reserved 663552K
  class space    used 19870K, capacity 21945K, committed 22004K, reserved 516096K
}
Event: 90.825 GC heap after
{Heap after GC invocations=56 (full 0):
 garbage-first heap   total 339968K, used 140446K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 161795K, capacity 167202K, committed 167480K, reserved 663552K
  class space    used 19870K, capacity 21945K, committed 22004K, reserved 516096K
}
Event: 91.186 GC heap before
{Heap before GC invocations=56 (full 0):
 garbage-first heap   total 339968K, used 266398K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 130 young (133120K), 7 survivors (7168K)
 Metaspace       used 161795K, capacity 167202K, committed 167480K, reserved 663552K
  class space    used 19870K, capacity 21945K, committed 22004K, reserved 516096K
}
Event: 91.196 GC heap after
{Heap after GC invocations=57 (full 0):
 garbage-first heap   total 339968K, used 141278K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 161795K, capacity 167202K, committed 167480K, reserved 663552K
  class space    used 19870K, capacity 21945K, committed 22004K, reserved 516096K
}
Event: 91.466 GC heap before
{Heap before GC invocations=57 (full 0):
 garbage-first heap   total 339968K, used 271326K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 135 young (138240K), 8 survivors (8192K)
 Metaspace       used 161804K, capacity 167202K, committed 167480K, reserved 663552K
  class space    used 19870K, capacity 21945K, committed 22004K, reserved 516096K
}

Deoptimization events (10 events):
Event: 90.155 Thread 0x000001fb5e070000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001fb49d24690 method=org.jetbrains.kotlin.protobuf.FieldSet.isInitialized(Ljava/util/Map$Entry;)Z @ 61 c2
Event: 90.155 Thread 0x000001fb5e070000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001fb49d24690 method=org.jetbrains.kotlin.protobuf.FieldSet.isInitialized(Ljava/util/Map$Entry;)Z @ 61 c2
Event: 90.392 Thread 0x000001fb5e070000 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000001fb49d75a90 method=org.jetbrains.org.objectweb.asm.ClassReader.accept(Lorg/jetbrains/org/objectweb/asm/ClassVisitor;[Lorg/jetbrains/org/objectweb/asm/Attribute;I)V @ 816 c2
Event: 90.392 Thread 0x000001fb5e071000 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000001fb49d75a90 method=org.jetbrains.org.objectweb.asm.ClassReader.accept(Lorg/jetbrains/org/objectweb/asm/ClassVisitor;[Lorg/jetbrains/org/objectweb/asm/Attribute;I)V @ 816 c2
Event: 90.570 Thread 0x000001fb5e071000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001fb49d848c8 method=org.jetbrains.org.objectweb.asm.ClassReader.readMethod(Lorg/jetbrains/org/objectweb/asm/ClassVisitor;Lorg/jetbrains/org/objectweb/asm/Context;I)I @ 378 c2
Event: 90.605 Thread 0x000001fb5e070000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001fb49d25ed8 method=org.jetbrains.kotlin.protobuf.CodedInputStream.readRawVarint32()I @ 10 c2
Event: 90.607 Thread 0x000001fb5e070000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001fb49cd8dcc method=org.jetbrains.kotlin.protobuf.CodedInputStream.readRawVarint32()I @ 10 c2
Event: 90.873 Thread 0x000001fb5b5ef800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001fb49d2c2f0 method=org.jetbrains.org.objectweb.asm.SymbolTable.addConstantUtf8Reference(ILjava/lang/String;)Lorg/jetbrains/org/objectweb/asm/Symbol; @ 45 c2
Event: 90.873 Thread 0x000001fb5b5ef800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001fb49d0f83c method=org.jetbrains.org.objectweb.asm.SymbolTable.addConstantUtf8(Ljava/lang/String;)I @ 40 c2
Event: 91.357 Thread 0x000001fb5e070000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001fb49d65b08 method=org.jetbrains.kotlin.metadata.ProtoBuf$Property.<init>(Lorg/jetbrains/kotlin/protobuf/CodedInputStream;Lorg/jetbrains/kotlin/protobuf/ExtensionRegistryLite;)V @ 54 c2

Classes redefined (0 events):
No events

Internal exceptions (10 events):
Event: 64.681 Thread 0x000001fb63713800 Exception <a 'sun/nio/fs/WindowsException'{0x000000008fbd3a10}> (0x000000008fbd3a10) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 611]
Event: 64.776 Thread 0x000001fb5aedb800 Exception <a 'java/lang/NoSuchMethodError'{0x000000008cb58610}: static Lorg/gradle/internal/build/event/types/DefaultFileDownloadDescriptor;.<clinit>()V> (0x000000008cb58610) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 1371]
Event: 64.912 Thread 0x000001fb5b5e9800 Exception <a 'sun/nio/fs/WindowsException'{0x000000008c7c6528}> (0x000000008c7c6528) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 611]
Event: 64.922 Thread 0x000001fb5b5e9800 Implicit null exception at 0x000001fb49530c74 to 0x000001fb49531d3c
Event: 66.437 Thread 0x000001fb5aedb800 Exception <a 'java/lang/NoSuchMethodError'{0x000000008f5b1450}: static Lorg/gradle/internal/build/event/types/DefaultStatusEvent;.<clinit>()V> (0x000000008f5b1450) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 1371]
Event: 84.281 Thread 0x000001fb5aedb800 Exception <a 'java/lang/NoSuchMethodError'{0x000000008f9c5228}: static Lorg/gradle/internal/build/event/types/DefaultFileDownloadSuccessResult;.<clinit>()V> (0x000000008f9c5228) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 1371]
Event: 86.496 Thread 0x000001fb5b5eb000 Exception <a 'java/lang/NoSuchMethodError'{0x000000008e8884c0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, int)'> (0x000000008e8884c0) thrown at [./open/src/hotspot/share/interpreter/linkResolver.c
Event: 86.865 Thread 0x000001fb5e070000 Exception <a 'java/lang/NoSuchMethodError'{0x000000008f192700}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, int)'> (0x000000008f192700) thrown at [./open/src/hotspot/share/interpreter/linkResolver.
Event: 87.157 Thread 0x000001fb5e070000 Implicit null exception at 0x000001fb497cc7b4 to 0x000001fb497cd3ec
Event: 87.213 Thread 0x000001fb5b5eb000 Exception <a 'java/lang/NoSuchMethodError'{0x000000008e4aacc0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000008e4aacc0) thrown at [./open/src/hotspot/share/interpreter/

Events (10 events):
Event: 90.873 Thread 0x000001fb5b5ef800 DEOPT UNPACKING pc=0x000001fb417765af sp=0x0000000e78cfafd8 mode 2
Event: 90.873 Thread 0x000001fb5b5ef800 Uncommon trap: trap_request=0xffffff4d fr.pc=0x000001fb49d0f83c relative=0x0000000000000c5c
Event: 90.873 Thread 0x000001fb5b5ef800 DEOPT PACKING pc=0x000001fb49d0f83c sp=0x0000000e78cfaf60
Event: 90.873 Thread 0x000001fb5b5ef800 DEOPT UNPACKING pc=0x000001fb417765af sp=0x0000000e78cfaf40 mode 2
Event: 91.186 Executing VM operation: G1CollectForAllocation
Event: 91.197 Executing VM operation: G1CollectForAllocation done
Event: 91.357 Thread 0x000001fb5e070000 Uncommon trap: trap_request=0xffffff4d fr.pc=0x000001fb49d65b08 relative=0x0000000000000fa8
Event: 91.357 Thread 0x000001fb5e070000 DEOPT PACKING pc=0x000001fb49d65b08 sp=0x0000000e790fa090
Event: 91.357 Thread 0x000001fb5e070000 DEOPT UNPACKING pc=0x000001fb417765af sp=0x0000000e790fa060 mode 2
Event: 91.466 Executing VM operation: G1CollectForAllocation


Dynamic libraries:
0x00007ff659a60000 - 0x00007ff659a70000 	C:\Program Files\Java\jdk-11.0.25\bin\java.exe
0x00007fff7f780000 - 0x00007fff7f9e5000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fff7ebe0000 - 0x00007fff7eca9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fff7ccf0000 - 0x00007fff7d0d8000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fff7d340000 - 0x00007fff7d48b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff6c250000 - 0x00007fff6c26b000 	C:\Program Files\Java\jdk-11.0.25\bin\VCRUNTIME140.dll
0x00007fff6c370000 - 0x00007fff6c389000 	C:\Program Files\Java\jdk-11.0.25\bin\jli.dll
0x00007fff7f490000 - 0x00007fff7f543000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fff7ed90000 - 0x00007fff7ee39000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fff7f3d0000 - 0x00007fff7f476000 	C:\WINDOWS\System32\sechost.dll
0x00007fff7d520000 - 0x00007fff7d635000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fff7f1a0000 - 0x00007fff7f36a000 	C:\WINDOWS\System32\USER32.dll
0x00007fff660e0000 - 0x00007fff6637a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007fff7d260000 - 0x00007fff7d287000 	C:\WINDOWS\System32\win32u.dll
0x00007fff7f710000 - 0x00007fff7f73b000 	C:\WINDOWS\System32\GDI32.dll
0x00007fff7c8d0000 - 0x00007fff7ca07000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fff7d290000 - 0x00007fff7d333000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fff6de40000 - 0x00007fff6de4b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fff7ecd0000 - 0x00007fff7ed00000 	C:\WINDOWS\System32\IMM32.DLL
0x00007fff764a0000 - 0x00007fff764ac000 	C:\Program Files\Java\jdk-11.0.25\bin\vcruntime140_1.dll
0x00007fff51890000 - 0x00007fff5191e000 	C:\Program Files\Java\jdk-11.0.25\bin\msvcp140.dll
0x00007fff0ec90000 - 0x00007fff0f7f3000 	C:\Program Files\Java\jdk-11.0.25\bin\server\jvm.dll
0x00007fff7dbf0000 - 0x00007fff7dbf8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007fff58c00000 - 0x00007fff58c0a000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007fff666f0000 - 0x00007fff66725000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fff7eeb0000 - 0x00007fff7ef24000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fff7b7b0000 - 0x00007fff7b7cb000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fff76440000 - 0x00007fff76450000 	C:\Program Files\Java\jdk-11.0.25\bin\verify.dll
0x00007fff76bf0000 - 0x00007fff76e31000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fff7d860000 - 0x00007fff7dbe5000 	C:\WINDOWS\System32\combase.dll
0x00007fff7f0b0000 - 0x00007fff7f191000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fff61080000 - 0x00007fff610b9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fff7ca10000 - 0x00007fff7caa9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fff67cc0000 - 0x00007fff67ce8000 	C:\Program Files\Java\jdk-11.0.25\bin\java.dll
0x00007fff6f470000 - 0x00007fff6f47a000 	C:\Program Files\Java\jdk-11.0.25\bin\jimage.dll
0x00007fff76450000 - 0x00007fff7645e000 	C:\Program Files\Java\jdk-11.0.25\bin\instrument.dll
0x00007fff6bdd0000 - 0x00007fff6bde7000 	C:\Program Files\Java\jdk-11.0.25\bin\zip.dll
0x00007fff7e140000 - 0x00007fff7e882000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fff7d0e0000 - 0x00007fff7d254000 	C:\WINDOWS\System32\wintypes.dll
0x00007fff7a590000 - 0x00007fff7ade8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fff7f580000 - 0x00007fff7f671000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fff7d640000 - 0x00007fff7d6aa000 	C:\WINDOWS\System32\shlwapi.dll
0x00007fff7c7e0000 - 0x00007fff7c80f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff6bbd0000 - 0x00007fff6bbe9000 	C:\Program Files\Java\jdk-11.0.25\bin\net.dll
0x00007fff76ff0000 - 0x00007fff7710e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007fff7bd20000 - 0x00007fff7bd8a000 	C:\WINDOWS\system32\mswsock.dll
0x00007fff67d80000 - 0x00007fff67d92000 	C:\Program Files\Java\jdk-11.0.25\bin\nio.dll
0x00007fff66b80000 - 0x00007fff66ba7000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00000000718d0000 - 0x0000000071943000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007fff6c200000 - 0x00007fff6c209000 	C:\Program Files\Java\jdk-11.0.25\bin\management.dll
0x00007fff6bc90000 - 0x00007fff6bc9b000 	C:\Program Files\Java\jdk-11.0.25\bin\management_ext.dll
0x00007fff7bfd0000 - 0x00007fff7bfeb000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fff7b710000 - 0x00007fff7b74a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fff7bdc0000 - 0x00007fff7bdeb000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fff7c7b0000 - 0x00007fff7c7d6000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007fff7bff0000 - 0x00007fff7bffc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fff7b150000 - 0x00007fff7b183000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fff7f480000 - 0x00007fff7f48a000 	C:\WINDOWS\System32\NSI.dll
0x00007fff6dd30000 - 0x00007fff6dd4f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007fff6dd00000 - 0x00007fff6dd25000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007fff7b1e0000 - 0x00007fff7b307000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x0000000071850000 - 0x00000000718c3000 	C:\Users\<USER>\AppData\Local\Temp\native-platform14778668133212728738dir\gradle-fileevents.dll
0x00007fff57dc0000 - 0x00007fff57dd8000 	C:\WINDOWS\system32\napinsp.dll
0x00007fff57de0000 - 0x00007fff57df2000 	C:\WINDOWS\System32\winrnr.dll
0x00007fff57d80000 - 0x00007fff57db0000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007fff6de50000 - 0x00007fff6de70000 	C:\WINDOWS\system32\wshbth.dll
0x00007fff6d8f0000 - 0x00007fff6d8fb000 	C:\Windows\System32\rasadhlp.dll
0x00007fff6d540000 - 0x00007fff6d5c6000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007fff76390000 - 0x00007fff7639e000 	C:\Program Files\Java\jdk-11.0.25\bin\sunmscapi.dll
0x00007fff7cb70000 - 0x00007fff7cce7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007fff7c1f0000 - 0x00007fff7c220000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007fff7c1a0000 - 0x00007fff7c1df000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007fff654f0000 - 0x00007fff65517000 	C:\Program Files\Java\jdk-11.0.25\bin\sunec.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-11.0.25\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;C:\Program Files\Java\jdk-11.0.25\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform14778668133212728738dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=512m -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 528482304                                 {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxMetaspaceSize                         = 536870912                                 {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5836300                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122910970                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122910970                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-11.0.25
PATH=C:\Gradle\gradle-8.13\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\Java\jdk-11.0.25\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\SqlCmd\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64\compiler;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Intel\WiFi\bin\;C:\Program Files\Common Files\Intel\WirelessCommon\;C:\ProgramData\chocolatey\bin;C:\Python312\Scripts;C:\Python312;C:\Program Files\dotnet\;C:\xampp\php\php.exe;C:\php;C:\tools\php84;C:\xampp\mysql\bin;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\ncat;C:\curl;C:\Users\<USER>\AppData\Roaming\npm\node_modules;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Program Files\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundatio;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\Gradle\gradle-8.13\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\Scripts;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files (x86)\Nmap;C:\xampp\mysql\b;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\MongoDB\Server\7.0\bin;C:\Program Files\MongoDB\Server\8.0\bin;;C:\PostgreSQL\16\bin;C:\Program Files\SqlCmd\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\Com
USERNAME=Sheldon
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 10, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 11 , 64 bit Build 26100 (10.0.26100.4202)

CPU:total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 142 stepping 10 microcode 0xf6, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, rtm, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, fma

Memory: 4k page, system-wide physical 16222M (1164M free)
TotalPageFile size 21072M (AvailPageFile size 81M)
current process WorkingSet (physical memory assigned to process): 762M, peak: 770M
current process commit charge ("private bytes"): 807M, peak: 921M

vm_info: Java HotSpot(TM) 64-Bit Server VM (11.0.25+9-LTS-256) for windows-amd64 JRE (11.0.25+9-LTS-256), built on Sep 30 2024 06:30:20 by "mach5one" with MS VC++ 17.6 (VS2022)

END.
