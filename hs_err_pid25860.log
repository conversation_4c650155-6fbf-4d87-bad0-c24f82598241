#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1409936 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (./open/src/hotspot/share/memory/arena.cpp:197), pid=25860, tid=26276
#
# JRE version: Java(TM) SE Runtime Environment 18.9 (11.0.25+9) (build 11.0.25+9-LTS-256)
# Java VM: Java HotSpot(TM) 64-Bit Server VM 18.9 (11.0.25+9-LTS-256, mixed mode, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=512m -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: Intel(R) Core(TM) i7-8650U CPU @ 1.90GHz, 8 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Tue Jul  1 12:45:21 2025 E. Africa Standard Time elapsed time: 42.511634 seconds (0d 0h 0m 42s)

---------------  T H R E A D  ---------------

Current thread (0x000001e97f552000):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=26276, stack(0x0000003a7f000000,0x0000003a7f100000)]


Current CompileTask:
C2:  42511 11723       4       org.jetbrains.org.objectweb.asm.ClassWriter::toByteArray (1535 bytes)

Stack: [0x0000003a7f000000,0x0000003a7f100000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x65824a]
V  [jvm.dll+0x79ae8f]
V  [jvm.dll+0x79c559]
V  [jvm.dll+0x79cc03]
V  [jvm.dll+0x255785]
V  [jvm.dll+0xb312c]
V  [jvm.dll+0xb373c]
V  [jvm.dll+0x2c6b92]
V  [jvm.dll+0x567d87]
V  [jvm.dll+0x217e21]
V  [jvm.dll+0x211663]
V  [jvm.dll+0x20dfd4]
V  [jvm.dll+0x18b381]
V  [jvm.dll+0x21e774]
V  [jvm.dll+0x21ca9c]
V  [jvm.dll+0x75ed51]
V  [jvm.dll+0x757674]
V  [jvm.dll+0x6570f5]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001e917847750, length=144, elements={
0x000001e97150b000, 0x000001e97f534000, 0x000001e97f535800, 0x000001e97f54e000,
0x000001e97f54f000, 0x000001e97f550800, 0x000001e97f552000, 0x000001e97f554800,
0x000001e97f5a6000, 0x000001e97f703800, 0x000001e911eee800, 0x000001e912535800,
0x000001e912ef0800, 0x000001e9112ae000, 0x000001e911be4000, 0x000001e9112ef000,
0x000001e91278a000, 0x000001e912845000, 0x000001e912846000, 0x000001e912847000,
0x000001e912088800, 0x000001e912089800, 0x000001e912087000, 0x000001e91208a800,
0x000001e912088000, 0x000001e91208b000, 0x000001e91208d000, 0x000001e91208d800,
0x000001e91208c000, 0x000001e91124d000, 0x000001e91124c000, 0x000001e91124b800,
0x000001e91124a800, 0x000001e911250800, 0x000001e91124e000, 0x000001e91124e800,
0x000001e911252000, 0x000001e91124f800, 0x000001e911253000, 0x000001e911253800,
0x000001e911254800, 0x000001e911257000, 0x000001e911256000, 0x000001e911255000,
0x000001e911257800, 0x000001e911258800, 0x000001e911259800, 0x000001e9184d0000,
0x000001e9184d3800, 0x000001e9184d1000, 0x000001e9184d2800, 0x000001e9184d2000,
0x000001e9184d6000, 0x000001e9184d4800, 0x000001e9184d5000, 0x000001e9184d6800,
0x000001e918cb1000, 0x000001e918cb2000, 0x000001e918cb0000, 0x000001e918cb2800,
0x000001e918cb5000, 0x000001e918cb3800, 0x000001e918cb7000, 0x000001e918cb4800,
0x000001e918cb7800, 0x000001e918cbb000, 0x000001e918cb9000, 0x000001e918cb8800,
0x000001e918cbc800, 0x000001e918cba000, 0x000001e918cbb800, 0x000001e918cbe000,
0x000001e918cbd800, 0x000001e918cbf000, 0x000001e915187800, 0x000001e915188800,
0x000001e915186000, 0x000001e915187000, 0x000001e91518c000, 0x000001e91518a000,
0x000001e915189800, 0x000001e91518b000, 0x000001e91518c800, 0x000001e9192d2000,
0x000001e9192d1000, 0x000001e9192d0800, 0x000001e9192d3800, 0x000001e9192d2800,
0x000001e9192d7000, 0x000001e9192d6000, 0x000001e9192d5000, 0x000001e9192d4800,
0x000001e915216000, 0x000001e915217000, 0x000001e91521a000, 0x000001e915219800,
0x000001e915218800, 0x000001e915217800, 0x000001e91521c800, 0x000001e91521d800,
0x000001e91521b000, 0x000001e91521e000, 0x000001e91521b800, 0x000001e915220800,
0x000001e915221800, 0x000001e91521f000, 0x000001e915222000, 0x000001e915220000,
0x000001e915224800, 0x000001e915223000, 0x000001e915224000, 0x000001e916304800,
0x000001e916302800, 0x000001e916306000, 0x000001e916303800, 0x000001e916306800,
0x000001e916307800, 0x000001e916305000, 0x000001e91630b000, 0x000001e916309000,
0x000001e916308800, 0x000001e91630c800, 0x000001e91630d000, 0x000001e91630e000,
0x000001e91630b800, 0x000001e916310800, 0x000001e91630f000, 0x000001e916311800,
0x000001e91630f800, 0x000001e91590a000, 0x000001e91590c000, 0x000001e91590c800,
0x000001e91590b000, 0x000001e91590d800, 0x000001e915911000, 0x000001e91590e800,
0x000001e915911800, 0x000001e91590f000, 0x000001e915912800, 0x000001e915910000,
0x000001e915915800, 0x000001e915913000, 0x000001e913507800, 0x000001e915793800
}

Java Threads: ( => current thread )
  0x000001e97150b000 JavaThread "main" [_thread_blocked, id=13968, stack(0x0000003a7e400000,0x0000003a7e500000)]
  0x000001e97f534000 JavaThread "Reference Handler" daemon [_thread_blocked, id=24064, stack(0x0000003a7eb00000,0x0000003a7ec00000)]
  0x000001e97f535800 JavaThread "Finalizer" daemon [_thread_blocked, id=4948, stack(0x0000003a7ec00000,0x0000003a7ed00000)]
  0x000001e97f54e000 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=25964, stack(0x0000003a7ed00000,0x0000003a7ee00000)]
  0x000001e97f54f000 JavaThread "Attach Listener" daemon [_thread_blocked, id=20180, stack(0x0000003a7ee00000,0x0000003a7ef00000)]
  0x000001e97f550800 JavaThread "Service Thread" daemon [_thread_blocked, id=23408, stack(0x0000003a7ef00000,0x0000003a7f000000)]
=>0x000001e97f552000 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=26276, stack(0x0000003a7f000000,0x0000003a7f100000)]
  0x000001e97f554800 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=10608, stack(0x0000003a7f100000,0x0000003a7f200000)]
  0x000001e97f5a6000 JavaThread "Sweeper thread" daemon [_thread_blocked, id=17996, stack(0x0000003a7f200000,0x0000003a7f300000)]
  0x000001e97f703800 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=22072, stack(0x0000003a7f300000,0x0000003a7f400000)]
  0x000001e911eee800 JavaThread "Daemon health stats" [_thread_blocked, id=20820, stack(0x0000003a7f400000,0x0000003a7f500000)]
  0x000001e912535800 JavaThread "Incoming local TCP Connector on port 39548" [_thread_in_native, id=19492, stack(0x0000003a7f600000,0x0000003a7f700000)]
  0x000001e912ef0800 JavaThread "Daemon periodic checks" [_thread_blocked, id=19820, stack(0x0000003a7fd00000,0x0000003a7fe00000)]
  0x000001e9112ae000 JavaThread "Daemon" [_thread_blocked, id=20500, stack(0x0000003a7fe00000,0x0000003a7ff00000)]
  0x000001e911be4000 JavaThread "Handler for socket connection from /127.0.0.1:39548 to /127.0.0.1:39549" [_thread_in_native, id=23680, stack(0x0000003a7ff00000,0x0000003a80000000)]
  0x000001e9112ef000 JavaThread "Cancel handler" [_thread_blocked, id=26376, stack(0x0000003a00000000,0x0000003a00100000)]
  0x000001e91278a000 JavaThread "Daemon worker" [_thread_blocked, id=23436, stack(0x0000003a00100000,0x0000003a00200000)]
  0x000001e912845000 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:39548 to /127.0.0.1:39549" [_thread_blocked, id=10636, stack(0x0000003a00200000,0x0000003a00300000)]
  0x000001e912846000 JavaThread "Stdin handler" [_thread_blocked, id=25820, stack(0x0000003a00300000,0x0000003a00400000)]
  0x000001e912847000 JavaThread "Daemon client event forwarder" [_thread_blocked, id=25536, stack(0x0000003a00400000,0x0000003a00500000)]
  0x000001e912088800 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=26524, stack(0x0000003a00800000,0x0000003a00900000)]
  0x000001e912089800 JavaThread "File lock request listener" [_thread_in_native, id=25432, stack(0x0000003a00900000,0x0000003a00a00000)]
  0x000001e912087000 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)" [_thread_blocked, id=24704, stack(0x0000003a00a00000,0x0000003a00b00000)]
  0x000001e91208a800 JavaThread "Problems report writer" [_thread_blocked, id=15928, stack(0x0000003a00d00000,0x0000003a00e00000)]
  0x000001e912088000 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\android\.gradle\8.12\fileHashes)" [_thread_blocked, id=22056, stack(0x0000003a00e00000,0x0000003a00f00000)]
  0x000001e91208b000 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=9028, stack(0x0000003a00f00000,0x0000003a01000000)]
  0x000001e91208d000 JavaThread "File watcher server" daemon [_thread_in_native, id=22956, stack(0x0000003a01000000,0x0000003a01100000)]
  0x000001e91208d800 JavaThread "File watcher consumer" daemon [_thread_blocked, id=1256, stack(0x0000003a01100000,0x0000003a01200000)]
  0x000001e91208c000 JavaThread "jar transforms" [_thread_blocked, id=19536, stack(0x0000003a01200000,0x0000003a01300000)]
  0x000001e91124d000 JavaThread "jar transforms Thread 2" [_thread_blocked, id=20880, stack(0x0000003a01300000,0x0000003a01400000)]
  0x000001e91124c000 JavaThread "jar transforms Thread 3" [_thread_blocked, id=13788, stack(0x0000003a01400000,0x0000003a01500000)]
  0x000001e91124b800 JavaThread "jar transforms Thread 4" [_thread_blocked, id=24920, stack(0x0000003a01500000,0x0000003a01600000)]
  0x000001e91124a800 JavaThread "jar transforms Thread 5" [_thread_blocked, id=24856, stack(0x0000003a01600000,0x0000003a01700000)]
  0x000001e911250800 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\android\.gradle\8.12\checksums)" [_thread_blocked, id=22092, stack(0x0000003a01700000,0x0000003a01800000)]
  0x000001e91124e000 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)" [_thread_blocked, id=20080, stack(0x0000003a01800000,0x0000003a01900000)]
  0x000001e91124e800 JavaThread "jar transforms Thread 6" [_thread_blocked, id=26436, stack(0x0000003a01900000,0x0000003a01a00000)]
  0x000001e911252000 JavaThread "jar transforms Thread 7" [_thread_blocked, id=16196, stack(0x0000003a01a00000,0x0000003a01b00000)]
  0x000001e91124f800 JavaThread "jar transforms Thread 8" [_thread_blocked, id=928, stack(0x0000003a01b00000,0x0000003a01c00000)]
  0x000001e911253000 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.12\md-rule)" [_thread_blocked, id=23812, stack(0x0000003a01c00000,0x0000003a01d00000)]
  0x000001e911253800 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.12\md-supplier)" [_thread_blocked, id=17932, stack(0x0000003a01d00000,0x0000003a01e00000)]
  0x000001e911254800 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)" [_thread_blocked, id=23700, stack(0x0000003a01e00000,0x0000003a01f00000)]
  0x000001e911257000 JavaThread "Unconstrained build operations" [_thread_blocked, id=26444, stack(0x0000003a00c00000,0x0000003a00d00000)]
  0x000001e911256000 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=25100, stack(0x0000003a01f00000,0x0000003a02000000)]
  0x000001e911255000 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=23484, stack(0x0000003a02000000,0x0000003a02100000)]
  0x000001e911257800 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=25548, stack(0x0000003a02100000,0x0000003a02200000)]
  0x000001e911258800 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=22972, stack(0x0000003a02200000,0x0000003a02300000)]
  0x000001e911259800 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=13912, stack(0x0000003a02300000,0x0000003a02400000)]
  0x000001e9184d0000 JavaThread "Unconstrained build operations Thread 7" [_thread_in_native, id=25416, stack(0x0000003a02500000,0x0000003a02600000)]
  0x000001e9184d3800 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=22812, stack(0x0000003a02600000,0x0000003a02700000)]
  0x000001e9184d1000 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=7064, stack(0x0000003a02700000,0x0000003a02800000)]
  0x000001e9184d2800 JavaThread "Unconstrained build operations Thread 10" [_thread_in_Java, id=24440, stack(0x0000003a02800000,0x0000003a02900000)]
  0x000001e9184d2000 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=21984, stack(0x0000003a02900000,0x0000003a02a00000)]
  0x000001e9184d6000 JavaThread "Unconstrained build operations Thread 12" [_thread_in_Java, id=11276, stack(0x0000003a02a00000,0x0000003a02b00000)]
  0x000001e9184d4800 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=23668, stack(0x0000003a02b00000,0x0000003a02c00000)]
  0x000001e9184d5000 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=21308, stack(0x0000003a02c00000,0x0000003a02d00000)]
  0x000001e9184d6800 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=23560, stack(0x0000003a02d00000,0x0000003a02e00000)]
  0x000001e918cb1000 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=23188, stack(0x0000003a02e00000,0x0000003a02f00000)]
  0x000001e918cb2000 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=17160, stack(0x0000003a02f00000,0x0000003a03000000)]
  0x000001e918cb0000 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=16144, stack(0x0000003a03000000,0x0000003a03100000)]
  0x000001e918cb2800 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=14984, stack(0x0000003a03100000,0x0000003a03200000)]
  0x000001e918cb5000 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=11736, stack(0x0000003a03200000,0x0000003a03300000)]
  0x000001e918cb3800 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=25888, stack(0x0000003a03300000,0x0000003a03400000)]
  0x000001e918cb7000 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=24640, stack(0x0000003a03400000,0x0000003a03500000)]
  0x000001e918cb4800 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=24364, stack(0x0000003a03500000,0x0000003a03600000)]
  0x000001e918cb7800 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=18292, stack(0x0000003a03600000,0x0000003a03700000)]
  0x000001e918cbb000 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=24928, stack(0x0000003a03700000,0x0000003a03800000)]
  0x000001e918cb9000 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=14444, stack(0x0000003a03800000,0x0000003a03900000)]
  0x000001e918cb8800 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=19328, stack(0x0000003a03900000,0x0000003a03a00000)]
  0x000001e918cbc800 JavaThread "build event listener" [_thread_blocked, id=2396, stack(0x0000003a02400000,0x0000003a02500000)]
  0x000001e918cba000 JavaThread "Memory manager" [_thread_blocked, id=6332, stack(0x0000003a03a00000,0x0000003a03b00000)]
  0x000001e918cbb800 JavaThread "ForkJoinPool.commonPool-worker-3" daemon [_thread_blocked, id=19612, stack(0x0000003a03c00000,0x0000003a03d00000)]
  0x000001e918cbe000 JavaThread "ForkJoinPool.commonPool-worker-5" daemon [_thread_blocked, id=26264, stack(0x0000003a03d00000,0x0000003a03e00000)]
  0x000001e918cbd800 JavaThread "ForkJoinPool.commonPool-worker-7" daemon [_thread_blocked, id=17844, stack(0x0000003a03e00000,0x0000003a03f00000)]
  0x000001e918cbf000 JavaThread "ForkJoinPool.commonPool-worker-9" daemon [_thread_blocked, id=1148, stack(0x0000003a03f00000,0x0000003a04000000)]
  0x000001e915187800 JavaThread "ForkJoinPool.commonPool-worker-11" daemon [_thread_blocked, id=16152, stack(0x0000003a04000000,0x0000003a04100000)]
  0x000001e915188800 JavaThread "ForkJoinPool.commonPool-worker-13" daemon [_thread_blocked, id=26544, stack(0x0000003a04100000,0x0000003a04200000)]
  0x000001e915186000 JavaThread "ForkJoinPool.commonPool-worker-15" daemon [_thread_blocked, id=18356, stack(0x0000003a04200000,0x0000003a04300000)]
  0x000001e915187000 JavaThread "Exec process" [_thread_blocked, id=16588, stack(0x0000003a04300000,0x0000003a04400000)]
  0x000001e91518c000 JavaThread "Exec process Thread 2" [_thread_blocked, id=10204, stack(0x0000003a04400000,0x0000003a04500000)]
  0x000001e91518a000 JavaThread "Exec process Thread 3" [_thread_blocked, id=7612, stack(0x0000003a04500000,0x0000003a04600000)]
  0x000001e915189800 JavaThread "included builds" [_thread_blocked, id=20568, stack(0x0000003a04600000,0x0000003a04700000)]
  0x000001e91518b000 JavaThread "Execution worker" [_thread_blocked, id=17904, stack(0x0000003a04700000,0x0000003a04800000)]
  0x000001e91518c800 JavaThread "Execution worker Thread 2" [_thread_blocked, id=1208, stack(0x0000003a04800000,0x0000003a04900000)]
  0x000001e9192d2000 JavaThread "Execution worker Thread 3" [_thread_blocked, id=19196, stack(0x0000003a04900000,0x0000003a04a00000)]
  0x000001e9192d1000 JavaThread "Execution worker Thread 4" [_thread_blocked, id=26068, stack(0x0000003a04a00000,0x0000003a04b00000)]
  0x000001e9192d0800 JavaThread "Execution worker Thread 5" [_thread_blocked, id=20980, stack(0x0000003a04b00000,0x0000003a04c00000)]
  0x000001e9192d3800 JavaThread "Execution worker Thread 6" [_thread_blocked, id=12568, stack(0x0000003a04c00000,0x0000003a04d00000)]
  0x000001e9192d2800 JavaThread "Execution worker Thread 7" [_thread_blocked, id=24632, stack(0x0000003a04d00000,0x0000003a04e00000)]
  0x000001e9192d7000 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\node_modules\@react-native\gradle-plugin\.gradle\8.12\executionHistory)" [_thread_blocked, id=9900, stack(0x0000003a04e00000,0x0000003a04f00000)]
  0x000001e9192d6000 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=14468, stack(0x0000003a04f00000,0x0000003a05000000)]
  0x000001e9192d5000 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=22068, stack(0x0000003a05000000,0x0000003a05100000)]
  0x000001e9192d4800 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=10184, stack(0x0000003a05100000,0x0000003a05200000)]
  0x000001e915216000 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=25792, stack(0x0000003a05200000,0x0000003a05300000)]
  0x000001e915217000 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=14000, stack(0x0000003a05300000,0x0000003a05400000)]
  0x000001e91521a000 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=15104, stack(0x0000003a05400000,0x0000003a05500000)]
  0x000001e915219800 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=25448, stack(0x0000003a05500000,0x0000003a05600000)]
  0x000001e915218800 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=26588, stack(0x0000003a05600000,0x0000003a05700000)]
  0x000001e915217800 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=14904, stack(0x0000003a05700000,0x0000003a05800000)]
  0x000001e91521c800 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=26492, stack(0x0000003a05800000,0x0000003a05900000)]
  0x000001e91521d800 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=23060, stack(0x0000003a05900000,0x0000003a05a00000)]
  0x000001e91521b000 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=19468, stack(0x0000003a05a00000,0x0000003a05b00000)]
  0x000001e91521e000 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=18336, stack(0x0000003a05b00000,0x0000003a05c00000)]
  0x000001e91521b800 JavaThread "Unconstrained build operations Thread 41" [_thread_blocked, id=23808, stack(0x0000003a05c00000,0x0000003a05d00000)]
  0x000001e915220800 JavaThread "Unconstrained build operations Thread 42" [_thread_blocked, id=18528, stack(0x0000003a05d00000,0x0000003a05e00000)]
  0x000001e915221800 JavaThread "Unconstrained build operations Thread 43" [_thread_blocked, id=24352, stack(0x0000003a05e00000,0x0000003a05f00000)]
  0x000001e91521f000 JavaThread "Unconstrained build operations Thread 44" [_thread_blocked, id=18052, stack(0x0000003a05f00000,0x0000003a06000000)]
  0x000001e915222000 JavaThread "Unconstrained build operations Thread 45" [_thread_blocked, id=14864, stack(0x0000003a06000000,0x0000003a06100000)]
  0x000001e915220000 JavaThread "Unconstrained build operations Thread 46" [_thread_blocked, id=8100, stack(0x0000003a06100000,0x0000003a06200000)]
  0x000001e915224800 JavaThread "Unconstrained build operations Thread 47" [_thread_blocked, id=14632, stack(0x0000003a06200000,0x0000003a06300000)]
  0x000001e915223000 JavaThread "Unconstrained build operations Thread 48" [_thread_blocked, id=19948, stack(0x0000003a06300000,0x0000003a06400000)]
  0x000001e915224000 JavaThread "Unconstrained build operations Thread 49" [_thread_blocked, id=23404, stack(0x0000003a00b00000,0x0000003a00c00000)]
  0x000001e916304800 JavaThread "Unconstrained build operations Thread 50" [_thread_blocked, id=12848, stack(0x0000003a03b00000,0x0000003a03c00000)]
  0x000001e916302800 JavaThread "Unconstrained build operations Thread 51" [_thread_blocked, id=26056, stack(0x0000003a06400000,0x0000003a06500000)]
  0x000001e916306000 JavaThread "Unconstrained build operations Thread 52" [_thread_blocked, id=21408, stack(0x0000003a06500000,0x0000003a06600000)]
  0x000001e916303800 JavaThread "Unconstrained build operations Thread 53" [_thread_blocked, id=12076, stack(0x0000003a06600000,0x0000003a06700000)]
  0x000001e916306800 JavaThread "Unconstrained build operations Thread 54" [_thread_blocked, id=15896, stack(0x0000003a06700000,0x0000003a06800000)]
  0x000001e916307800 JavaThread "Unconstrained build operations Thread 55" [_thread_blocked, id=26224, stack(0x0000003a06800000,0x0000003a06900000)]
  0x000001e916305000 JavaThread "Unconstrained build operations Thread 56" [_thread_blocked, id=2892, stack(0x0000003a06900000,0x0000003a06a00000)]
  0x000001e91630b000 JavaThread "Unconstrained build operations Thread 57" [_thread_blocked, id=25264, stack(0x0000003a06b00000,0x0000003a06c00000)]
  0x000001e916309000 JavaThread "Unconstrained build operations Thread 58" [_thread_blocked, id=22100, stack(0x0000003a06c00000,0x0000003a06d00000)]
  0x000001e916308800 JavaThread "Unconstrained build operations Thread 59" [_thread_blocked, id=24932, stack(0x0000003a06d00000,0x0000003a06e00000)]
  0x000001e91630c800 JavaThread "Unconstrained build operations Thread 60" [_thread_blocked, id=26364, stack(0x0000003a06e00000,0x0000003a06f00000)]
  0x000001e91630d000 JavaThread "Unconstrained build operations Thread 61" [_thread_blocked, id=24824, stack(0x0000003a06f00000,0x0000003a07000000)]
  0x000001e91630e000 JavaThread "Unconstrained build operations Thread 62" [_thread_blocked, id=6760, stack(0x0000003a07000000,0x0000003a07100000)]
  0x000001e91630b800 JavaThread "Unconstrained build operations Thread 63" [_thread_blocked, id=9596, stack(0x0000003a07100000,0x0000003a07200000)]
  0x000001e916310800 JavaThread "Unconstrained build operations Thread 64" [_thread_blocked, id=15520, stack(0x0000003a07300000,0x0000003a07400000)]
  0x000001e91630f000 JavaThread "Unconstrained build operations Thread 65" [_thread_blocked, id=26352, stack(0x0000003a07400000,0x0000003a07500000)]
  0x000001e916311800 JavaThread "Unconstrained build operations Thread 66" [_thread_blocked, id=16036, stack(0x0000003a07500000,0x0000003a07600000)]
  0x000001e91630f800 JavaThread "Unconstrained build operations Thread 67" [_thread_blocked, id=24728, stack(0x0000003a07600000,0x0000003a07700000)]
  0x000001e91590a000 JavaThread "Unconstrained build operations Thread 68" [_thread_blocked, id=6268, stack(0x0000003a07700000,0x0000003a07800000)]
  0x000001e91590c000 JavaThread "Unconstrained build operations Thread 69" [_thread_blocked, id=25372, stack(0x0000003a07800000,0x0000003a07900000)]
  0x000001e91590c800 JavaThread "Unconstrained build operations Thread 70" [_thread_blocked, id=5564, stack(0x0000003a07900000,0x0000003a07a00000)]
  0x000001e91590b000 JavaThread "Unconstrained build operations Thread 71" [_thread_blocked, id=2596, stack(0x0000003a07a00000,0x0000003a07b00000)]
  0x000001e91590d800 JavaThread "Unconstrained build operations Thread 72" [_thread_blocked, id=13700, stack(0x0000003a07b00000,0x0000003a07c00000)]
  0x000001e915911000 JavaThread "Unconstrained build operations Thread 73" [_thread_blocked, id=10824, stack(0x0000003a07c00000,0x0000003a07d00000)]
  0x000001e91590e800 JavaThread "Unconstrained build operations Thread 74" [_thread_blocked, id=11728, stack(0x0000003a07d00000,0x0000003a07e00000)]
  0x000001e915911800 JavaThread "Unconstrained build operations Thread 75" [_thread_blocked, id=18516, stack(0x0000003a07e00000,0x0000003a07f00000)]
  0x000001e91590f000 JavaThread "Unconstrained build operations Thread 76" [_thread_blocked, id=14976, stack(0x0000003a07f00000,0x0000003a08000000)]
  0x000001e915912800 JavaThread "Unconstrained build operations Thread 77" [_thread_blocked, id=5964, stack(0x0000003a08000000,0x0000003a08100000)]
  0x000001e915910000 JavaThread "Unconstrained build operations Thread 78" [_thread_blocked, id=25648, stack(0x0000003a07200000,0x0000003a07300000)]
  0x000001e915915800 JavaThread "Unconstrained build operations Thread 79" [_thread_blocked, id=7768, stack(0x0000003a08300000,0x0000003a08400000)]
  0x000001e915913000 JavaThread "Unconstrained build operations Thread 80" [_thread_blocked, id=18416, stack(0x0000003a08400000,0x0000003a08500000)]
  0x000001e913507800 JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=17960, stack(0x0000003a06a00000,0x0000003a06b00000)]
  0x000001e915793800 JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=24852, stack(0x0000003a08500000,0x0000003a08600000)]

Other Threads:
  0x000001e97f52e800 VMThread "VM Thread" [stack: 0x0000003a7ea00000,0x0000003a7eb00000] [id=26088]
  0x000001e97f893000 WatcherThread [stack: 0x0000003a7f500000,0x0000003a7f600000] [id=26280]
  0x000001e971522800 GCTaskThread "GC Thread#0" [stack: 0x0000003a7e500000,0x0000003a7e600000] [id=19168]
  0x000001e911615000 GCTaskThread "GC Thread#1" [stack: 0x0000003a7f700000,0x0000003a7f800000] [id=22932]
  0x000001e911615800 GCTaskThread "GC Thread#2" [stack: 0x0000003a7f800000,0x0000003a7f900000] [id=19052]
  0x000001e9114a5800 GCTaskThread "GC Thread#3" [stack: 0x0000003a7f900000,0x0000003a7fa00000] [id=25724]
  0x000001e9114a6000 GCTaskThread "GC Thread#4" [stack: 0x0000003a7fa00000,0x0000003a7fb00000] [id=8772]
  0x000001e911631000 GCTaskThread "GC Thread#5" [stack: 0x0000003a7fb00000,0x0000003a7fc00000] [id=6244]
  0x000001e911d02000 GCTaskThread "GC Thread#6" [stack: 0x0000003a00500000,0x0000003a00600000] [id=10468]
  0x000001e9123c5000 GCTaskThread "GC Thread#7" [stack: 0x0000003a00600000,0x0000003a00700000] [id=19988]
  0x000001e971555000 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000003a7e600000,0x0000003a7e700000] [id=25276]
  0x000001e971557000 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000003a7e700000,0x0000003a7e800000] [id=25232]
  0x000001e9120af000 ConcurrentGCThread "G1 Conc#1" [stack: 0x0000003a00700000,0x0000003a00800000] [id=25196]
  0x000001e97eb96800 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000003a7e800000,0x0000003a7e900000] [id=9996]
  0x000001e917893800 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000003a08600000,0x0000003a08700000] [id=5000]
  0x000001e97eb9c000 ConcurrentGCThread "G1 Young RemSet Sampling" [stack: 0x0000003a7e900000,0x0000003a7ea00000] [id=20960]

Threads with active compile tasks:
C2 CompilerThread0    42602 11723       4       org.jetbrains.org.objectweb.asm.ClassWriter::toByteArray (1535 bytes)
C2 CompilerThread1    42602 11660       4       org.jetbrains.org.objectweb.asm.ClassReader::readMethod (1061 bytes)
C2 CompilerThread2    42602 11495       4       org.jetbrains.org.objectweb.asm.ClassReader::accept (1380 bytes)

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 528482304 Address: 0x0000000100000000

Heap:
 garbage-first heap   total 260096K, used 197364K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 107 young (109568K), 4 survivors (4096K)
 Metaspace       used 92105K, capacity 96279K, committed 96540K, reserved 600064K
  class space    used 12068K, capacity 13595K, committed 13640K, reserved 516096K
Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, A=archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%| O|  |TAMS 0x0000000080100000, 0x0000000080000000| Untracked 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%| O|  |TAMS 0x0000000080200000, 0x0000000080100000| Untracked 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HS|  |TAMS 0x0000000080300000, 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080400000, 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%|HC|  |TAMS 0x0000000080500000, 0x0000000080400000| Complete 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%|HS|  |TAMS 0x0000000080600000, 0x0000000080500000| Complete 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080700000, 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080800000, 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000, 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000, 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000, 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000, 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080d00000, 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000, 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080f00000, 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000, 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000, 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000, 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081300000, 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081400000, 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081500000, 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081600000, 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081700000, 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081800000, 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081900000, 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081a00000, 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081b00000, 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081c00000, 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081d00000, 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081e00000, 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081f00000, 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000082000000, 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082051400, 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%|HS|  |TAMS 0x0000000082100000, 0x0000000082100000| Complete 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%|HC|  |TAMS 0x0000000082200000, 0x0000000082200000| Complete 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%|HS|  |TAMS 0x0000000082300000, 0x0000000082300000| Complete 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%|HC|  |TAMS 0x0000000082400000, 0x0000000082400000| Complete 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%|HC|  |TAMS 0x0000000082500000, 0x0000000082500000| Complete 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%|HS|  |TAMS 0x0000000082600000, 0x0000000082600000| Complete 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%|HC|  |TAMS 0x0000000082700000, 0x0000000082700000| Complete 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082800000, 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082900000, 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082a00000, 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%|HS|  |TAMS 0x0000000082b00000, 0x0000000082b00000| Complete 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%|HC|  |TAMS 0x0000000082c00000, 0x0000000082c00000| Complete 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%|HC|  |TAMS 0x0000000082d00000, 0x0000000082d00000| Complete 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%| O|  |TAMS 0x0000000082e00000, 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%| O|  |TAMS 0x0000000082f00000, 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%| O|  |TAMS 0x0000000083000000, 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%| O|  |TAMS 0x0000000083100000, 0x0000000083100000| Untracked 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%| O|  |TAMS 0x0000000083200000, 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%| O|  |TAMS 0x0000000083300000, 0x0000000083300000| Untracked 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%| O|  |TAMS 0x0000000083400000, 0x0000000083400000| Untracked 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%| O|  |TAMS 0x0000000083500000, 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%| O|  |TAMS 0x0000000083600000, 0x0000000083600000| Untracked 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%| O|  |TAMS 0x0000000083700000, 0x0000000083700000| Untracked 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%| O|  |TAMS 0x0000000083800000, 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%| O|  |TAMS 0x0000000083900000, 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%| O|  |TAMS 0x0000000083a00000, 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%|HS|  |TAMS 0x0000000083b00000, 0x0000000083b00000| Complete 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%|HS|  |TAMS 0x0000000083c00000, 0x0000000083c00000| Complete 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083d00000, 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%| O|  |TAMS 0x0000000083e00000, 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000083f00000, 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%| O|  |TAMS 0x0000000084000000, 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084100000, 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084200000, 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084300000, 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084400000, 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084500000, 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084600000, 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084700000, 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084800000, 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%| O|  |TAMS 0x0000000084900000, 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084a00000, 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084b00000, 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084c00000, 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084d00000, 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084e00000, 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000084f00000, 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085000000, 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085100000, 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085200000, 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085300000, 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085400000, 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085500000, 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085600000, 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085700000, 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085863a00, 0x0000000085900000| 38%| O|  |TAMS 0x0000000085800000, 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085900000, 0x0000000085a00000|  0%| F|  |TAMS 0x0000000085900000, 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085a00000, 0x0000000085b00000|  0%| F|  |TAMS 0x0000000085a00000, 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085b00000, 0x0000000085c00000|  0%| F|  |TAMS 0x0000000085b00000, 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085c00000, 0x0000000085d00000|  0%| F|  |TAMS 0x0000000085c00000, 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085d00000, 0x0000000085e00000|  0%| F|  |TAMS 0x0000000085d00000, 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085e00000, 0x0000000085f00000|  0%| F|  |TAMS 0x0000000085e00000, 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000085f00000, 0x0000000086000000|  0%| F|  |TAMS 0x0000000085f00000, 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086000000, 0x0000000086100000|  0%| F|  |TAMS 0x0000000086000000, 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086100000, 0x0000000086200000|  0%| F|  |TAMS 0x0000000086100000, 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086200000, 0x0000000086300000|  0%| F|  |TAMS 0x0000000086200000, 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086300000, 0x0000000086400000|  0%| F|  |TAMS 0x0000000086300000, 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086400000, 0x0000000086500000|  0%| F|  |TAMS 0x0000000086400000, 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086500000, 0x0000000086600000|  0%| F|  |TAMS 0x0000000086500000, 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086600000, 0x0000000086700000|  0%| F|  |TAMS 0x0000000086600000, 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086700000, 0x0000000086800000|  0%| F|  |TAMS 0x0000000086700000, 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086800000, 0x0000000086900000|  0%| F|  |TAMS 0x0000000086800000, 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086900000, 0x0000000086a00000|  0%| F|  |TAMS 0x0000000086900000, 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086a00000, 0x0000000086b00000|  0%| F|  |TAMS 0x0000000086a00000, 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086b00000, 0x0000000086c00000|  0%| F|  |TAMS 0x0000000086b00000, 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086c00000, 0x0000000086d00000|  0%| F|  |TAMS 0x0000000086c00000, 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086d00000, 0x0000000086e00000|  0%| F|  |TAMS 0x0000000086d00000, 0x0000000086d00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086e00000, 0x0000000086f00000|  0%| F|  |TAMS 0x0000000086e00000, 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000086f00000, 0x0000000087000000|  0%| F|  |TAMS 0x0000000086f00000, 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087000000, 0x0000000087100000|  0%| F|  |TAMS 0x0000000087000000, 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087100000, 0x0000000087200000|  0%| F|  |TAMS 0x0000000087100000, 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087200000, 0x0000000087300000|  0%| F|  |TAMS 0x0000000087200000, 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087300000, 0x0000000087400000|  0%| F|  |TAMS 0x0000000087300000, 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087400000, 0x0000000087500000|  0%| F|  |TAMS 0x0000000087400000, 0x0000000087400000| Untracked 
| 117|0x0000000087500000, 0x0000000087500000, 0x0000000087600000|  0%| F|  |TAMS 0x0000000087500000, 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087600000, 0x0000000087700000|  0%| F|  |TAMS 0x0000000087600000, 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087700000, 0x0000000087800000|  0%| F|  |TAMS 0x0000000087700000, 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087800000, 0x0000000087900000|  0%| F|  |TAMS 0x0000000087800000, 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087900000, 0x0000000087a00000|  0%| F|  |TAMS 0x0000000087900000, 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087a00000, 0x0000000087b00000|  0%| F|  |TAMS 0x0000000087a00000, 0x0000000087a00000| Untracked 
| 123|0x0000000087b00000, 0x0000000087b00000, 0x0000000087c00000|  0%| F|  |TAMS 0x0000000087b00000, 0x0000000087b00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087c00000, 0x0000000087d00000|  0%| F|  |TAMS 0x0000000087c00000, 0x0000000087c00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087d00000, 0x0000000087e00000|  0%| F|  |TAMS 0x0000000087d00000, 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087e00000, 0x0000000087f00000|  0%| F|  |TAMS 0x0000000087e00000, 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000087f00000, 0x0000000088000000|  0%| F|  |TAMS 0x0000000087f00000, 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088000000, 0x0000000088100000|  0%| F|  |TAMS 0x0000000088000000, 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088100000, 0x0000000088200000|  0%| F|  |TAMS 0x0000000088100000, 0x0000000088100000| Untracked 
| 130|0x0000000088200000, 0x0000000088200000, 0x0000000088300000|  0%| F|  |TAMS 0x0000000088200000, 0x0000000088200000| Untracked 
| 131|0x0000000088300000, 0x0000000088300000, 0x0000000088400000|  0%| F|  |TAMS 0x0000000088300000, 0x0000000088300000| Untracked 
| 132|0x0000000088400000, 0x00000000884598b0, 0x0000000088500000| 34%| S|CS|TAMS 0x0000000088400000, 0x0000000088400000| Complete 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%| S|CS|TAMS 0x0000000088500000, 0x0000000088500000| Complete 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%| S|CS|TAMS 0x0000000088600000, 0x0000000088600000| Complete 
| 135|0x0000000088700000, 0x0000000088700000, 0x0000000088800000|  0%| F|  |TAMS 0x0000000088700000, 0x0000000088700000| Untracked 
| 136|0x0000000088800000, 0x0000000088800000, 0x0000000088900000|  0%| F|  |TAMS 0x0000000088800000, 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088900000, 0x0000000088a00000|  0%| F|  |TAMS 0x0000000088900000, 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| S|CS|TAMS 0x0000000088a00000, 0x0000000088a00000| Complete 
| 139|0x0000000088b00000, 0x0000000088b00000, 0x0000000088c00000|  0%| F|  |TAMS 0x0000000088b00000, 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088c00000, 0x0000000088d00000|  0%| F|  |TAMS 0x0000000088c00000, 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088d00000, 0x0000000088e00000|  0%| F|  |TAMS 0x0000000088d00000, 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088e00000, 0x0000000088f00000|  0%| F|  |TAMS 0x0000000088e00000, 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000088f00000, 0x0000000089000000|  0%| F|  |TAMS 0x0000000088f00000, 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089000000, 0x0000000089100000|  0%| F|  |TAMS 0x0000000089000000, 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089100000, 0x0000000089200000|  0%| F|  |TAMS 0x0000000089100000, 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089200000, 0x0000000089300000|  0%| F|  |TAMS 0x0000000089200000, 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089300000, 0x0000000089400000|  0%| F|  |TAMS 0x0000000089300000, 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089400000, 0x0000000089500000|  0%| F|  |TAMS 0x0000000089400000, 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089580800, 0x0000000089600000| 50%| E|  |TAMS 0x0000000089500000, 0x0000000089500000| Complete 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%| E|CS|TAMS 0x0000000089600000, 0x0000000089600000| Complete 
| 151|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%| E|CS|TAMS 0x0000000089700000, 0x0000000089700000| Complete 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| E|CS|TAMS 0x0000000089800000, 0x0000000089800000| Complete 
| 153|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%| E|CS|TAMS 0x0000000089900000, 0x0000000089900000| Complete 
| 154|0x0000000089a00000, 0x0000000089b00000, 0x0000000089b00000|100%| E|CS|TAMS 0x0000000089a00000, 0x0000000089a00000| Complete 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%| E|CS|TAMS 0x0000000089b00000, 0x0000000089b00000| Complete 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| E|CS|TAMS 0x0000000089c00000, 0x0000000089c00000| Complete 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%| E|CS|TAMS 0x0000000089d00000, 0x0000000089d00000| Complete 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%| E|CS|TAMS 0x0000000089e00000, 0x0000000089e00000| Complete 
| 159|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%| E|CS|TAMS 0x0000000089f00000, 0x0000000089f00000| Complete 
| 160|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%| E|CS|TAMS 0x000000008a000000, 0x000000008a000000| Complete 
| 161|0x000000008a100000, 0x000000008a200000, 0x000000008a200000|100%| E|CS|TAMS 0x000000008a100000, 0x000000008a100000| Complete 
| 162|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%| E|CS|TAMS 0x000000008a200000, 0x000000008a200000| Complete 
| 163|0x000000008a300000, 0x000000008a400000, 0x000000008a400000|100%| E|CS|TAMS 0x000000008a300000, 0x000000008a300000| Complete 
| 164|0x000000008a400000, 0x000000008a500000, 0x000000008a500000|100%| E|CS|TAMS 0x000000008a400000, 0x000000008a400000| Complete 
| 165|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%| E|CS|TAMS 0x000000008a500000, 0x000000008a500000| Complete 
| 166|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%| E|CS|TAMS 0x000000008a600000, 0x000000008a600000| Complete 
| 167|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%| E|CS|TAMS 0x000000008a700000, 0x000000008a700000| Complete 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%| E|CS|TAMS 0x000000008a800000, 0x000000008a800000| Complete 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%| E|CS|TAMS 0x000000008a900000, 0x000000008a900000| Complete 
| 170|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%| E|CS|TAMS 0x000000008aa00000, 0x000000008aa00000| Complete 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%| E|CS|TAMS 0x000000008ab00000, 0x000000008ab00000| Complete 
| 172|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%| E|CS|TAMS 0x000000008ac00000, 0x000000008ac00000| Complete 
| 173|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%| E|CS|TAMS 0x000000008ad00000, 0x000000008ad00000| Complete 
| 174|0x000000008ae00000, 0x000000008af00000, 0x000000008af00000|100%| E|CS|TAMS 0x000000008ae00000, 0x000000008ae00000| Complete 
| 175|0x000000008af00000, 0x000000008b000000, 0x000000008b000000|100%| E|CS|TAMS 0x000000008af00000, 0x000000008af00000| Complete 
| 176|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%| E|CS|TAMS 0x000000008b000000, 0x000000008b000000| Complete 
| 177|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%| E|CS|TAMS 0x000000008b100000, 0x000000008b100000| Complete 
| 178|0x000000008b200000, 0x000000008b300000, 0x000000008b300000|100%| E|CS|TAMS 0x000000008b200000, 0x000000008b200000| Complete 
| 179|0x000000008b300000, 0x000000008b400000, 0x000000008b400000|100%| E|CS|TAMS 0x000000008b300000, 0x000000008b300000| Complete 
| 180|0x000000008b400000, 0x000000008b500000, 0x000000008b500000|100%| E|CS|TAMS 0x000000008b400000, 0x000000008b400000| Complete 
| 181|0x000000008b500000, 0x000000008b600000, 0x000000008b600000|100%| E|CS|TAMS 0x000000008b500000, 0x000000008b500000| Complete 
| 182|0x000000008b600000, 0x000000008b700000, 0x000000008b700000|100%| E|CS|TAMS 0x000000008b600000, 0x000000008b600000| Complete 
| 183|0x000000008b700000, 0x000000008b800000, 0x000000008b800000|100%| E|CS|TAMS 0x000000008b700000, 0x000000008b700000| Complete 
| 184|0x000000008b800000, 0x000000008b900000, 0x000000008b900000|100%| E|CS|TAMS 0x000000008b800000, 0x000000008b800000| Complete 
| 185|0x000000008b900000, 0x000000008ba00000, 0x000000008ba00000|100%| E|CS|TAMS 0x000000008b900000, 0x000000008b900000| Complete 
| 186|0x000000008ba00000, 0x000000008bb00000, 0x000000008bb00000|100%| E|CS|TAMS 0x000000008ba00000, 0x000000008ba00000| Complete 
| 187|0x000000008bb00000, 0x000000008bc00000, 0x000000008bc00000|100%| E|CS|TAMS 0x000000008bb00000, 0x000000008bb00000| Complete 
| 188|0x000000008bc00000, 0x000000008bd00000, 0x000000008bd00000|100%| E|CS|TAMS 0x000000008bc00000, 0x000000008bc00000| Complete 
| 189|0x000000008bd00000, 0x000000008be00000, 0x000000008be00000|100%| E|CS|TAMS 0x000000008bd00000, 0x000000008bd00000| Complete 
| 190|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%| E|CS|TAMS 0x000000008be00000, 0x000000008be00000| Complete 
| 191|0x000000008bf00000, 0x000000008c000000, 0x000000008c000000|100%| E|CS|TAMS 0x000000008bf00000, 0x000000008bf00000| Complete 
| 192|0x000000008c000000, 0x000000008c100000, 0x000000008c100000|100%| E|CS|TAMS 0x000000008c000000, 0x000000008c000000| Complete 
| 193|0x000000008c100000, 0x000000008c200000, 0x000000008c200000|100%| E|CS|TAMS 0x000000008c100000, 0x000000008c100000| Complete 
| 194|0x000000008c200000, 0x000000008c300000, 0x000000008c300000|100%| E|CS|TAMS 0x000000008c200000, 0x000000008c200000| Complete 
| 195|0x000000008c300000, 0x000000008c400000, 0x000000008c400000|100%| E|CS|TAMS 0x000000008c300000, 0x000000008c300000| Complete 
| 196|0x000000008c400000, 0x000000008c500000, 0x000000008c500000|100%| E|CS|TAMS 0x000000008c400000, 0x000000008c400000| Complete 
| 197|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%| E|CS|TAMS 0x000000008c500000, 0x000000008c500000| Complete 
| 198|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%| E|CS|TAMS 0x000000008c600000, 0x000000008c600000| Complete 
| 199|0x000000008c700000, 0x000000008c800000, 0x000000008c800000|100%| E|CS|TAMS 0x000000008c700000, 0x000000008c700000| Complete 
| 200|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%| E|CS|TAMS 0x000000008c800000, 0x000000008c800000| Complete 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%| E|CS|TAMS 0x000000008c900000, 0x000000008c900000| Complete 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%| E|CS|TAMS 0x000000008ca00000, 0x000000008ca00000| Complete 
| 203|0x000000008cb00000, 0x000000008cc00000, 0x000000008cc00000|100%| E|CS|TAMS 0x000000008cb00000, 0x000000008cb00000| Complete 
| 204|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%| E|CS|TAMS 0x000000008cc00000, 0x000000008cc00000| Complete 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%| E|CS|TAMS 0x000000008cd00000, 0x000000008cd00000| Complete 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%| E|CS|TAMS 0x000000008ce00000, 0x000000008ce00000| Complete 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%| E|CS|TAMS 0x000000008cf00000, 0x000000008cf00000| Complete 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| E|CS|TAMS 0x000000008d000000, 0x000000008d000000| Complete 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| E|CS|TAMS 0x000000008d100000, 0x000000008d100000| Complete 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| E|CS|TAMS 0x000000008d200000, 0x000000008d200000| Complete 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| E|CS|TAMS 0x000000008d300000, 0x000000008d300000| Complete 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| E|CS|TAMS 0x000000008d400000, 0x000000008d400000| Complete 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| E|CS|TAMS 0x000000008d500000, 0x000000008d500000| Complete 
| 214|0x000000008d600000, 0x000000008d700000, 0x000000008d700000|100%| E|CS|TAMS 0x000000008d600000, 0x000000008d600000| Complete 
| 215|0x000000008d700000, 0x000000008d800000, 0x000000008d800000|100%| E|CS|TAMS 0x000000008d700000, 0x000000008d700000| Complete 
| 216|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%| E|CS|TAMS 0x000000008d800000, 0x000000008d800000| Complete 
| 217|0x000000008d900000, 0x000000008da00000, 0x000000008da00000|100%| E|CS|TAMS 0x000000008d900000, 0x000000008d900000| Complete 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| E|CS|TAMS 0x000000008da00000, 0x000000008da00000| Complete 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| E|CS|TAMS 0x000000008db00000, 0x000000008db00000| Complete 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| E|CS|TAMS 0x000000008dc00000, 0x000000008dc00000| Complete 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| E|CS|TAMS 0x000000008dd00000, 0x000000008dd00000| Complete 
| 222|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%| E|CS|TAMS 0x000000008de00000, 0x000000008de00000| Complete 
| 223|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%| E|CS|TAMS 0x000000008df00000, 0x000000008df00000| Complete 
| 224|0x000000008e000000, 0x000000008e100000, 0x000000008e100000|100%| E|CS|TAMS 0x000000008e000000, 0x000000008e000000| Complete 
| 225|0x000000008e100000, 0x000000008e200000, 0x000000008e200000|100%| E|CS|TAMS 0x000000008e100000, 0x000000008e100000| Complete 
| 226|0x000000008e200000, 0x000000008e300000, 0x000000008e300000|100%| E|CS|TAMS 0x000000008e200000, 0x000000008e200000| Complete 
| 227|0x000000008e300000, 0x000000008e400000, 0x000000008e400000|100%| E|CS|TAMS 0x000000008e300000, 0x000000008e300000| Complete 
| 228|0x000000008e400000, 0x000000008e500000, 0x000000008e500000|100%| E|CS|TAMS 0x000000008e400000, 0x000000008e400000| Complete 
| 229|0x000000008e500000, 0x000000008e600000, 0x000000008e600000|100%| E|CS|TAMS 0x000000008e500000, 0x000000008e500000| Complete 
| 230|0x000000008e600000, 0x000000008e700000, 0x000000008e700000|100%| E|CS|TAMS 0x000000008e600000, 0x000000008e600000| Complete 
| 231|0x000000008e700000, 0x000000008e800000, 0x000000008e800000|100%| E|CS|TAMS 0x000000008e700000, 0x000000008e700000| Complete 
| 232|0x000000008e800000, 0x000000008e900000, 0x000000008e900000|100%| E|CS|TAMS 0x000000008e800000, 0x000000008e800000| Complete 
| 233|0x000000008e900000, 0x000000008ea00000, 0x000000008ea00000|100%| E|CS|TAMS 0x000000008e900000, 0x000000008e900000| Complete 
| 234|0x000000008ea00000, 0x000000008eb00000, 0x000000008eb00000|100%| E|CS|TAMS 0x000000008ea00000, 0x000000008ea00000| Complete 
| 235|0x000000008eb00000, 0x000000008ec00000, 0x000000008ec00000|100%| E|CS|TAMS 0x000000008eb00000, 0x000000008eb00000| Complete 
| 236|0x000000008ec00000, 0x000000008ed00000, 0x000000008ed00000|100%| E|CS|TAMS 0x000000008ec00000, 0x000000008ec00000| Complete 
| 237|0x000000008ed00000, 0x000000008ee00000, 0x000000008ee00000|100%| E|CS|TAMS 0x000000008ed00000, 0x000000008ed00000| Complete 
| 238|0x000000008ee00000, 0x000000008ef00000, 0x000000008ef00000|100%| E|CS|TAMS 0x000000008ee00000, 0x000000008ee00000| Complete 
| 239|0x000000008ef00000, 0x000000008f000000, 0x000000008f000000|100%| E|CS|TAMS 0x000000008ef00000, 0x000000008ef00000| Complete 
| 240|0x000000008f000000, 0x000000008f100000, 0x000000008f100000|100%| E|CS|TAMS 0x000000008f000000, 0x000000008f000000| Complete 
| 241|0x000000008f100000, 0x000000008f200000, 0x000000008f200000|100%| E|CS|TAMS 0x000000008f100000, 0x000000008f100000| Complete 
| 242|0x000000008f200000, 0x000000008f300000, 0x000000008f300000|100%| E|CS|TAMS 0x000000008f200000, 0x000000008f200000| Complete 
| 243|0x000000008f300000, 0x000000008f400000, 0x000000008f400000|100%| E|CS|TAMS 0x000000008f300000, 0x000000008f300000| Complete 
| 244|0x000000008f400000, 0x000000008f500000, 0x000000008f500000|100%| E|CS|TAMS 0x000000008f400000, 0x000000008f400000| Complete 
| 245|0x000000008f500000, 0x000000008f600000, 0x000000008f600000|100%| E|CS|TAMS 0x000000008f500000, 0x000000008f500000| Complete 
| 246|0x000000008f600000, 0x000000008f700000, 0x000000008f700000|100%| E|CS|TAMS 0x000000008f600000, 0x000000008f600000| Complete 
| 247|0x000000008f700000, 0x000000008f800000, 0x000000008f800000|100%| E|CS|TAMS 0x000000008f700000, 0x000000008f700000| Complete 
| 248|0x000000008f800000, 0x000000008f900000, 0x000000008f900000|100%| E|CS|TAMS 0x000000008f800000, 0x000000008f800000| Complete 
| 249|0x000000008f900000, 0x000000008fa00000, 0x000000008fa00000|100%| E|CS|TAMS 0x000000008f900000, 0x000000008f900000| Complete 
| 250|0x000000008fa00000, 0x000000008fb00000, 0x000000008fb00000|100%| E|CS|TAMS 0x000000008fa00000, 0x000000008fa00000| Complete 
| 251|0x000000008fb00000, 0x000000008fc00000, 0x000000008fc00000|100%| E|CS|TAMS 0x000000008fb00000, 0x000000008fb00000| Complete 
| 252|0x000000008fc00000, 0x000000008fd00000, 0x000000008fd00000|100%| E|CS|TAMS 0x000000008fc00000, 0x000000008fc00000| Complete 
| 253|0x000000008fd00000, 0x000000008fe00000, 0x000000008fe00000|100%| E|  |TAMS 0x000000008fd00000, 0x000000008fd00000| Complete 

Card table byte_map: [0x000001e979a70000,0x000001e979e70000] _byte_map_base: 0x000001e979670000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001e97154c120, (CMBitMap*) 0x000001e97154c0e8
 Prev Bits: [0x000001e97c270000, 0x000001e97e270000)
 Next Bits: [0x000001e97a270000, 0x000001e97c270000)

Polling page: 0x000001e96fc30000

Metaspace:

Usage:
  Non-class:     80.75 MB capacity,    78.16 MB ( 97%) used,     2.21 MB (  3%) free+waste,   383.31 KB ( <1%) overhead. 
      Class:     13.28 MB capacity,    11.79 MB ( 89%) used,     1.31 MB ( 10%) free+waste,   185.56 KB (  1%) overhead. 
       Both:     94.02 MB capacity,    89.95 MB ( 96%) used,     3.52 MB (  4%) free+waste,   568.88 KB ( <1%) overhead. 

Virtual space:
  Non-class space:       82.00 MB reserved,      80.96 MB ( 99%) committed 
      Class space:      504.00 MB reserved,      13.32 MB (  3%) committed 
             Both:      586.00 MB reserved,      94.28 MB ( 16%) committed 

Chunk freelists:
   Non-Class:  3.00 KB
       Class:  640 bytes
        Both:  3.62 KB

CodeHeap 'non-profiled nmethods': size=120000Kb used=7436Kb max_used=7436Kb free=112563Kb
 bounds [0x000001e907ad0000, 0x000001e908220000, 0x000001e90f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=25284Kb max_used=25284Kb free=94716Kb
 bounds [0x000001e9005a0000, 0x000001e901e60000, 0x000001e907ad0000]
CodeHeap 'non-nmethods': size=5760Kb used=2288Kb max_used=2341Kb free=3472Kb
 bounds [0x000001e900000000, 0x000001e900270000, 0x000001e9005a0000]
 total_blobs=12529 nmethods=11634 adapters=806
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (10 events):
Event: 41.443 Thread 0x000001e97f554800 nmethod 11771 0x000001e901e4ca10 code [0x000001e901e4cbc0, 0x000001e901e4cd48]
Event: 41.443 Thread 0x000001e97f554800 11772       2       org.jetbrains.kotlin.metadata.ProtoBuf$Annotation$1::parsePartialFrom (11 bytes)
Event: 41.443 Thread 0x000001e97f554800 nmethod 11772 0x000001e901e4ce90 code [0x000001e901e4d040, 0x000001e901e4d1c8]
Event: 41.443 Thread 0x000001e97f554800 11773       2       org.jetbrains.kotlin.metadata.ProtoBuf$Annotation::<init> (7 bytes)
Event: 41.443 Thread 0x000001e97f554800 nmethod 11773 0x000001e901e4d290 code [0x000001e901e4d420, 0x000001e901e4d528]
Event: 41.461 Thread 0x000001e97f554800 11776       2       org.jetbrains.org.objectweb.asm.AnnotationWriter::visitArray (62 bytes)
Event: 41.462 Thread 0x000001e97f554800 nmethod 11776 0x000001e901e4d610 code [0x000001e901e4d7e0, 0x000001e901e4da18]
Event: 41.491 Thread 0x000001e97f554800 11777       3       org.jetbrains.kotlin.incremental.storage.CollectionExternalizerV2::save (85 bytes)
Event: 41.493 Thread 0x000001e97f554800 nmethod 11777 0x000001e901e4db90 code [0x000001e901e4dea0, 0x000001e901e4ef18]
Event: 42.146 Thread 0x000001e97f554800 11778       2       java.util.stream.Collectors$$Lambda$78/0x0000000100192c40::accept (11 bytes)

GC Heap History (10 events):
Event: 37.568 GC heap before
{Heap before GC invocations=14 (full 0):
 garbage-first heap   total 260096K, used 202971K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 146 young (149504K), 12 survivors (12288K)
 Metaspace       used 86788K, capacity 90724K, committed 91036K, reserved 593920K
  class space    used 11687K, capacity 13140K, committed 13256K, reserved 516096K
}
Event: 37.599 GC heap after
{Heap after GC invocations=15 (full 0):
 garbage-first heap   total 260096K, used 74033K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 86788K, capacity 90724K, committed 91036K, reserved 593920K
  class space    used 11687K, capacity 13140K, committed 13256K, reserved 516096K
}
Event: 40.239 GC heap before
{Heap before GC invocations=15 (full 0):
 garbage-first heap   total 260096K, used 208177K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 142 young (145408K), 14 survivors (14336K)
 Metaspace       used 91680K, capacity 95825K, committed 96028K, reserved 600064K
  class space    used 12050K, capacity 13595K, committed 13640K, reserved 516096K
}
Event: 40.278 GC heap after
{Heap after GC invocations=16 (full 0):
 garbage-first heap   total 260096K, used 90079K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 18 young (18432K), 18 survivors (18432K)
 Metaspace       used 91680K, capacity 95825K, committed 96028K, reserved 600064K
  class space    used 12050K, capacity 13595K, committed 13640K, reserved 516096K
}
Event: 40.680 GC heap before
{Heap before GC invocations=16 (full 0):
 garbage-first heap   total 260096K, used 203743K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 130 young (133120K), 18 survivors (18432K)
 Metaspace       used 91828K, capacity 96018K, committed 96284K, reserved 600064K
  class space    used 12053K, capacity 13595K, committed 13640K, reserved 516096K
}
Event: 40.703 GC heap after
{Heap after GC invocations=17 (full 0):
 garbage-first heap   total 260096K, used 93977K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 91828K, capacity 96018K, committed 96284K, reserved 600064K
  class space    used 12053K, capacity 13595K, committed 13640K, reserved 516096K
}
Event: 41.026 GC heap before
{Heap before GC invocations=17 (full 0):
 garbage-first heap   total 260096K, used 207641K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 116 young (118784K), 4 survivors (4096K)
 Metaspace       used 91938K, capacity 96087K, committed 96284K, reserved 600064K
  class space    used 12055K, capacity 13595K, committed 13640K, reserved 516096K
}
Event: 41.030 GC heap after
{Heap after GC invocations=18 (full 0):
 garbage-first heap   total 260096K, used 93158K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 91938K, capacity 96087K, committed 96284K, reserved 600064K
  class space    used 12055K, capacity 13595K, committed 13640K, reserved 516096K
}
Event: 41.330 GC heap before
{Heap before GC invocations=18 (full 0):
 garbage-first heap   total 260096K, used 208870K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 118 young (120832K), 3 survivors (3072K)
 Metaspace       used 92090K, capacity 96279K, committed 96540K, reserved 600064K
  class space    used 12068K, capacity 13595K, committed 13640K, reserved 516096K
}
Event: 41.334 GC heap after
{Heap after GC invocations=19 (full 0):
 garbage-first heap   total 260096K, used 93940K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 92090K, capacity 96279K, committed 96540K, reserved 600064K
  class space    used 12068K, capacity 13595K, committed 13640K, reserved 516096K
}

Deoptimization events (10 events):
Event: 40.085 Thread 0x000001e9184d6000 Uncommon trap: reason=unstable_fused_if action=reinterpret pc=0x000001e9081d7868 method=org.jetbrains.org.objectweb.asm.ByteVector.putUTF8(Ljava/lang/String;)Lorg/jetbrains/org/objectweb/asm/ByteVector; @ 93 c2
Event: 40.211 Thread 0x000001e9184d6000 Uncommon trap: reason=unstable_fused_if action=reinterpret pc=0x000001e9081bee88 method=org.jetbrains.org.objectweb.asm.ByteVector.putUTF8(Ljava/lang/String;)Lorg/jetbrains/org/objectweb/asm/ByteVector; @ 93 c2
Event: 40.439 Thread 0x000001e9184d6000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001e9081e278c method=org.jetbrains.org.objectweb.asm.ClassReader.readElementValue(Lorg/jetbrains/org/objectweb/asm/AnnotationVisitor;ILjava/lang/String;[C)I @ 102 c2
Event: 40.513 Thread 0x000001e9184d0000 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001e907eeeb74 method=java.io.BufferedOutputStream.write([BII)V @ 6 c2
Event: 40.529 Thread 0x000001e9184d2800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001e907d78c04 method=java.util.zip.ZipFile$Source.getEntryPos([BZ)I @ 105 c2
Event: 40.554 Thread 0x000001e9184d2800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001e9081eb504 method=org.jetbrains.org.objectweb.asm.ByteVector.putUTF8(Ljava/lang/String;)Lorg/jetbrains/org/objectweb/asm/ByteVector; @ 93 c2
Event: 40.555 Thread 0x000001e9184d2800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001e9081e9728 method=org.jetbrains.org.objectweb.asm.ByteVector.putUTF8(Ljava/lang/String;)Lorg/jetbrains/org/objectweb/asm/ByteVector; @ 93 c2
Event: 40.942 Thread 0x000001e9184d0000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001e9081d61fc method=org.jetbrains.kotlin.protobuf.FieldSet.isInitialized(Ljava/util/Map$Entry;)Z @ 61 c2
Event: 40.943 Thread 0x000001e9184d0000 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001e9081d61fc method=org.jetbrains.kotlin.protobuf.FieldSet.isInitialized(Ljava/util/Map$Entry;)Z @ 61 c2
Event: 41.373 Thread 0x000001e9184d0000 Uncommon trap: reason=unstable_fused_if action=reinterpret pc=0x000001e9081f4784 method=org.jetbrains.kotlin.metadata.ProtoBuf$Type.<init>(Lorg/jetbrains/kotlin/protobuf/CodedInputStream;Lorg/jetbrains/kotlin/protobuf/ExtensionRegistryLite;)V @ 49 c2

Classes redefined (0 events):
No events

Internal exceptions (10 events):
Event: 37.371 Thread 0x000001e91278a000 Exception <a 'java/lang/ClassNotFoundException'{0x0000000087d8a8e0}: org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandler_DecoratedBeanInfo> (0x0000000087d8a8e0) thrown at [./open/src/hotspot/share/classfile/systemDictionary.cpp, li
Event: 37.374 Thread 0x000001e91278a000 Exception <a 'java/lang/ClassNotFoundException'{0x0000000087ddb8e8}: org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandlerBeanInfo> (0x0000000087ddb8e8) thrown at [./open/src/hotspot/share/classfile/systemDictionary.cpp, line 226]
Event: 37.376 Thread 0x000001e91278a000 Exception <a 'java/lang/ClassNotFoundException'{0x0000000087c2d118}: org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandlerCustomizer> (0x0000000087c2d118) thrown at [./open/src/hotspot/share/classfile/systemDictionary.cpp, line 226]
Event: 37.380 Thread 0x000001e91278a000 Exception <a 'java/lang/ClassNotFoundException'{0x0000000087ca98c0}: org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandler_DecoratedCustomizer> (0x0000000087ca98c0) thrown at [./open/src/hotspot/share/classfile/systemDictionary.cpp, 
Event: 38.009 Thread 0x000001e91518b000 Exception <a 'sun/nio/fs/WindowsException'{0x000000008f3fdbc0}> (0x000000008f3fdbc0) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 611]
Event: 38.009 Thread 0x000001e91518b000 Exception <a 'sun/nio/fs/WindowsException'{0x000000008f214b20}> (0x000000008f214b20) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 611]
Event: 38.010 Thread 0x000001e91518b000 Exception <a 'sun/nio/fs/WindowsException'{0x000000008f2170d8}> (0x000000008f2170d8) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 611]
Event: 38.203 Thread 0x000001e9184d0000 Exception <a 'java/lang/NoSuchMethodError'{0x000000008e9ac490}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, int)'> (0x000000008e9ac490) thrown at [./open/src/hotspot/share/interpreter/linkResolver.c
Event: 39.545 Thread 0x000001e9184d6000 Exception <a 'java/lang/NoSuchMethodError'{0x000000008cb1bca0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, int)'> (0x000000008cb1bca0) thrown at [./open/src/hotspot/share/interpreter/linkResolver.
Event: 39.740 Thread 0x000001e9184d2800 Exception <a 'java/lang/NoSuchMethodError'{0x000000008c54c930}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x000000008c54c930) thrown at [./open/src/hotspot/share/interpreter/

Events (10 events):
Event: 41.450 Thread 0x000001e9184d2800 DEOPT PACKING pc=0x000001e901ddc7c4 sp=0x0000003a028fb400
Event: 41.450 Thread 0x000001e9184d2800 DEOPT UNPACKING pc=0x000001e900026d5e sp=0x0000003a028fa8e0 mode 0
Event: 41.469 Thread 0x000001e9184d2800 DEOPT PACKING pc=0x000001e901ddc7c4 sp=0x0000003a028fb400
Event: 41.469 Thread 0x000001e9184d2800 DEOPT UNPACKING pc=0x000001e900026d5e sp=0x0000003a028fa8e0 mode 0
Event: 41.485 Thread 0x000001e9184d0000 DEOPT PACKING pc=0x000001e901ddc7c4 sp=0x0000003a025faf20
Event: 41.485 Thread 0x000001e9184d0000 DEOPT UNPACKING pc=0x000001e900026d5e sp=0x0000003a025fa400 mode 0
Event: 41.506 Thread 0x000001e9184d6000 DEOPT PACKING pc=0x000001e901ddc7c4 sp=0x0000003a02afb070
Event: 41.506 Thread 0x000001e9184d6000 DEOPT UNPACKING pc=0x000001e900026d5e sp=0x0000003a02afa550 mode 0
Event: 41.564 Thread 0x000001e9184d6000 DEOPT PACKING pc=0x000001e901ddc7c4 sp=0x0000003a02afb070
Event: 41.564 Thread 0x000001e9184d6000 DEOPT UNPACKING pc=0x000001e900026d5e sp=0x0000003a02afa550 mode 0


Dynamic libraries:
0x00007ff659a60000 - 0x00007ff659a70000 	C:\Program Files\Java\jdk-11.0.25\bin\java.exe
0x00007fff7f780000 - 0x00007fff7f9e5000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fff7ebe0000 - 0x00007fff7eca9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fff7ccf0000 - 0x00007fff7d0d8000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fff7d340000 - 0x00007fff7d48b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff6c370000 - 0x00007fff6c389000 	C:\Program Files\Java\jdk-11.0.25\bin\jli.dll
0x00007fff6c250000 - 0x00007fff6c26b000 	C:\Program Files\Java\jdk-11.0.25\bin\VCRUNTIME140.dll
0x00007fff7f490000 - 0x00007fff7f543000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fff7ed90000 - 0x00007fff7ee39000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fff7f3d0000 - 0x00007fff7f476000 	C:\WINDOWS\System32\sechost.dll
0x00007fff7d520000 - 0x00007fff7d635000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fff7f1a0000 - 0x00007fff7f36a000 	C:\WINDOWS\System32\USER32.dll
0x00007fff7d260000 - 0x00007fff7d287000 	C:\WINDOWS\System32\win32u.dll
0x00007fff7f710000 - 0x00007fff7f73b000 	C:\WINDOWS\System32\GDI32.dll
0x00007fff660e0000 - 0x00007fff6637a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007fff7c8d0000 - 0x00007fff7ca07000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fff7d290000 - 0x00007fff7d333000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fff6de40000 - 0x00007fff6de4b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fff7ecd0000 - 0x00007fff7ed00000 	C:\WINDOWS\System32\IMM32.DLL
0x00007fff764a0000 - 0x00007fff764ac000 	C:\Program Files\Java\jdk-11.0.25\bin\vcruntime140_1.dll
0x00007fff51890000 - 0x00007fff5191e000 	C:\Program Files\Java\jdk-11.0.25\bin\msvcp140.dll
0x00007fff0ec90000 - 0x00007fff0f7f3000 	C:\Program Files\Java\jdk-11.0.25\bin\server\jvm.dll
0x00007fff7dbf0000 - 0x00007fff7dbf8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007fff58c00000 - 0x00007fff58c0a000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007fff7eeb0000 - 0x00007fff7ef24000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fff666f0000 - 0x00007fff66725000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fff7b7b0000 - 0x00007fff7b7cb000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fff76440000 - 0x00007fff76450000 	C:\Program Files\Java\jdk-11.0.25\bin\verify.dll
0x00007fff76bf0000 - 0x00007fff76e31000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fff7d860000 - 0x00007fff7dbe5000 	C:\WINDOWS\System32\combase.dll
0x00007fff7f0b0000 - 0x00007fff7f191000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fff61080000 - 0x00007fff610b9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fff7ca10000 - 0x00007fff7caa9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fff67cc0000 - 0x00007fff67ce8000 	C:\Program Files\Java\jdk-11.0.25\bin\java.dll
0x00007fff6f470000 - 0x00007fff6f47a000 	C:\Program Files\Java\jdk-11.0.25\bin\jimage.dll
0x00007fff76390000 - 0x00007fff7639e000 	C:\Program Files\Java\jdk-11.0.25\bin\instrument.dll
0x00007fff6bdd0000 - 0x00007fff6bde7000 	C:\Program Files\Java\jdk-11.0.25\bin\zip.dll
0x00007fff7e140000 - 0x00007fff7e882000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fff7d0e0000 - 0x00007fff7d254000 	C:\WINDOWS\System32\wintypes.dll
0x00007fff7a590000 - 0x00007fff7ade8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fff7f580000 - 0x00007fff7f671000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fff7d640000 - 0x00007fff7d6aa000 	C:\WINDOWS\System32\shlwapi.dll
0x00007fff7c7e0000 - 0x00007fff7c80f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff6bbd0000 - 0x00007fff6bbe9000 	C:\Program Files\Java\jdk-11.0.25\bin\net.dll
0x00007fff76ff0000 - 0x00007fff7710e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007fff7bd20000 - 0x00007fff7bd8a000 	C:\WINDOWS\system32\mswsock.dll
0x00007fff67d80000 - 0x00007fff67d92000 	C:\Program Files\Java\jdk-11.0.25\bin\nio.dll
0x00007fff66b80000 - 0x00007fff66ba7000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00000000718d0000 - 0x0000000071943000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007fff6c200000 - 0x00007fff6c209000 	C:\Program Files\Java\jdk-11.0.25\bin\management.dll
0x00007fff6bc90000 - 0x00007fff6bc9b000 	C:\Program Files\Java\jdk-11.0.25\bin\management_ext.dll
0x00007fff7bfd0000 - 0x00007fff7bfeb000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fff7b710000 - 0x00007fff7b74a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fff7bdc0000 - 0x00007fff7bdeb000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fff7c7b0000 - 0x00007fff7c7d6000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007fff7bff0000 - 0x00007fff7bffc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fff7b150000 - 0x00007fff7b183000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fff7f480000 - 0x00007fff7f48a000 	C:\WINDOWS\System32\NSI.dll
0x00007fff6dd30000 - 0x00007fff6dd4f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007fff6dd00000 - 0x00007fff6dd25000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007fff7b1e0000 - 0x00007fff7b307000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x0000000071850000 - 0x00000000718c3000 	C:\Users\<USER>\AppData\Local\Temp\native-platform14512091398111142361dir\gradle-fileevents.dll
0x00007fff57dc0000 - 0x00007fff57dd8000 	C:\WINDOWS\system32\napinsp.dll
0x00007fff57de0000 - 0x00007fff57df2000 	C:\WINDOWS\System32\winrnr.dll
0x00007fff57d80000 - 0x00007fff57db0000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007fff6de50000 - 0x00007fff6de70000 	C:\WINDOWS\system32\wshbth.dll
0x00007fff6d8f0000 - 0x00007fff6d8fb000 	C:\Windows\System32\rasadhlp.dll
0x00007fff6d540000 - 0x00007fff6d5c6000 	C:\WINDOWS\System32\fwpuclnt.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-11.0.25\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;C:\Program Files\Java\jdk-11.0.25\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform14512091398111142361dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=512m -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 528482304                                 {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxMetaspaceSize                         = 536870912                                 {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5836300                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122910970                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122910970                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-11.0.25
PATH=C:\Gradle\gradle-8.13\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\Java\jdk-11.0.25\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\SqlCmd\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64\compiler;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Intel\WiFi\bin\;C:\Program Files\Common Files\Intel\WirelessCommon\;C:\ProgramData\chocolatey\bin;C:\Python312\Scripts;C:\Python312;C:\Program Files\dotnet\;C:\xampp\php\php.exe;C:\php;C:\tools\php84;C:\xampp\mysql\bin;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\ncat;C:\curl;C:\Users\<USER>\AppData\Roaming\npm\node_modules;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Program Files\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundatio;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\Gradle\gradle-8.13\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\Scripts;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files (x86)\Nmap;C:\xampp\mysql\b;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\MongoDB\Server\7.0\bin;C:\Program Files\MongoDB\Server\8.0\bin;;C:\PostgreSQL\16\bin;C:\Program Files\SqlCmd\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\Com
USERNAME=Sheldon
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 10, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 11 , 64 bit Build 26100 (10.0.26100.4202)

CPU:total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 142 stepping 10 microcode 0xf6, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, rtm, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, fma

Memory: 4k page, system-wide physical 16222M (1233M free)
TotalPageFile size 21072M (AvailPageFile size 14M)
current process WorkingSet (physical memory assigned to process): 543M, peak: 546M
current process commit charge ("private bytes"): 592M, peak: 595M

vm_info: Java HotSpot(TM) 64-Bit Server VM (11.0.25+9-LTS-256) for windows-amd64 JRE (11.0.25+9-LTS-256), built on Sep 30 2024 06:30:20 by "mach5one" with MS VC++ 17.6 (VS2022)

END.
