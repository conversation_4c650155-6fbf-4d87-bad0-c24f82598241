#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 267456 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=8700, tid=25712
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\d2cab3d8955a161a36a6f404eeb8145d\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\d2cab3d8955a161a36a6f404eeb8145d\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-3c20df3b44f3691dd5d35870ad860669-sock

Host: Intel(R) Core(TM) i7-8650U CPU @ 1.90GHz, 8 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Tue Jul  1 12:34:43 2025 E. Africa Standard Time elapsed time: 7.047768 seconds (0d 0h 0m 7s)

---------------  T H R E A D  ---------------

Current thread (0x0000022630110dd0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=25712, stack(0x00000072ddb00000,0x00000072ddc00000) (1024K)]


Current CompileTask:
C2:7048 1621       4       lombok.patcher.scripts.AddFieldScript::patch (55 bytes)

Stack: [0x00000072ddb00000,0x00000072ddc00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x2f2c6d]
V  [jvm.dll+0x5f6bca]
V  [jvm.dll+0x250cb2]
V  [jvm.dll+0x25106f]
V  [jvm.dll+0x249934]
V  [jvm.dll+0x246fc4]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002268e7d14d0, length=12, elements={
0x000002262dd60390, 0x0000022630103f80, 0x0000022630107040, 0x0000022630109b90,
0x000002263010c6f0, 0x000002263010d140, 0x000002263010ffa0, 0x0000022630110dd0,
0x0000022689448120, 0x0000022630207530, 0x0000022689751fe0, 0x000002268e5fc2d0
}

Java Threads: ( => current thread )
  0x000002262dd60390 JavaThread "main"                              [_thread_blocked, id=26248, stack(0x00000072dd100000,0x00000072dd200000) (1024K)]
  0x0000022630103f80 JavaThread "Reference Handler"          daemon [_thread_blocked, id=22428, stack(0x00000072dd500000,0x00000072dd600000) (1024K)]
  0x0000022630107040 JavaThread "Finalizer"                  daemon [_thread_blocked, id=5560, stack(0x00000072dd600000,0x00000072dd700000) (1024K)]
  0x0000022630109b90 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=17724, stack(0x00000072dd700000,0x00000072dd800000) (1024K)]
  0x000002263010c6f0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=1064, stack(0x00000072dd800000,0x00000072dd900000) (1024K)]
  0x000002263010d140 JavaThread "Service Thread"             daemon [_thread_blocked, id=14404, stack(0x00000072dd900000,0x00000072dda00000) (1024K)]
  0x000002263010ffa0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=1860, stack(0x00000072dda00000,0x00000072ddb00000) (1024K)]
=>0x0000022630110dd0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=25712, stack(0x00000072ddb00000,0x00000072ddc00000) (1024K)]
  0x0000022689448120 JavaThread "C1 CompilerThread0"         daemon [_thread_in_vm, id=17792, stack(0x00000072ddc00000,0x00000072ddd00000) (1024K)]
  0x0000022630207530 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=26072, stack(0x00000072ddd00000,0x00000072dde00000) (1024K)]
  0x0000022689751fe0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=22300, stack(0x00000072dde00000,0x00000072ddf00000) (1024K)]
  0x000002268e5fc2d0 JavaThread "Active Thread: Equinox Container: fdaacdb0-869a-49a9-8992-fd19b5dbf879"        [_thread_blocked, id=20116, stack(0x00000072de600000,0x00000072de700000) (1024K)]
Total: 12

Other Threads:
  0x000002262dd314a0 VMThread "VM Thread"                           [id=25012, stack(0x00000072dd400000,0x00000072dd500000) (1024K)]
  0x000002263001cb20 WatcherThread "VM Periodic Task Thread"        [id=6332, stack(0x00000072dd300000,0x00000072dd400000) (1024K)]
  0x000002262dd31100 WorkerThread "GC Thread#0"                     [id=17580, stack(0x00000072dd200000,0x00000072dd300000) (1024K)]
  0x000002262dd31f80 WorkerThread "GC Thread#1"                     [id=13408, stack(0x00000072de000000,0x00000072de100000) (1024K)]
  0x000002268e087520 WorkerThread "GC Thread#2"                     [id=15936, stack(0x00000072de100000,0x00000072de200000) (1024K)]
  0x000002268e0866a0 WorkerThread "GC Thread#3"                     [id=21056, stack(0x00000072de200000,0x00000072de300000) (1024K)]
  0x000002268e085820 WorkerThread "GC Thread#4"                     [id=15748, stack(0x00000072de300000,0x00000072de400000) (1024K)]
  0x000002268e085480 WorkerThread "GC Thread#5"                     [id=26236, stack(0x00000072de400000,0x00000072de500000) (1024K)]
  0x000002268e086a40 WorkerThread "GC Thread#6"                     [id=26508, stack(0x00000072de500000,0x00000072de600000) (1024K)]
Total: 9

Threads with active compile tasks:
C2 CompilerThread0  7103 1621       4       lombok.patcher.scripts.AddFieldScript::patch (55 bytes)
C1 CompilerThread0  7103 1648       3       java.util.concurrent.locks.ReentrantReadWriteLock$ReadLock::lock (9 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fff104ace88] MethodCompileQueue_lock - owner thread: 0x0000022689448120
[0x00007fff104ae208] CodeCache_lock - owner thread: 0x0000022689448120
[0x00007fff104ae388] Compile_lock - owner thread: 0x0000022689448120

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000022647000000-0x0000022647ba0000-0x0000022647ba0000), size 12189696, SharedBaseAddress: 0x0000022647000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000022648000000-0x0000022688000000, reserved size: 1073741824
Narrow klass base: 0x0000022647000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 16222M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 29696K, used 22282K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 74% used [0x00000000d5580000,0x00000000d6800dc8,0x00000000d6e80000)
  from space 4096K, 81% used [0x00000000d6e80000,0x00000000d71c1c68,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 16K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080004000,0x0000000084300000)
 Metaspace       used 7088K, committed 7360K, reserved 1114112K
  class space    used 707K, committed 832K, reserved 1048576K

Card table byte_map: [0x000002262fb30000,0x000002262ff40000] _byte_map_base: 0x000002262f730000

Marking Bits: (ParMarkBitMap*) 0x00007fff104b31f0
 Begin Bits: [0x0000022642630000, 0x0000022644630000)
 End Bits:   [0x0000022644630000, 0x0000022646630000)

Polling page: 0x000002262f7c0000

Metaspace:

Usage:
  Non-class:      6.23 MB used.
      Class:    707.64 KB used.
       Both:      6.92 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       6.38 MB ( 10%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     832.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       7.19 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  9.39 MB
       Class:  15.00 MB
        Both:  24.40 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 212.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 115.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 366.
num_chunk_merges: 0.
num_chunk_splits: 237.
num_chunks_enlarged: 157.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=702Kb max_used=702Kb free=119297Kb
 bounds [0x000002263ae50000, 0x000002263b0c0000, 0x0000022642380000]
CodeHeap 'profiled nmethods': size=120000Kb used=2879Kb max_used=2879Kb free=117120Kb
 bounds [0x0000022633380000, 0x0000022633650000, 0x000002263a8b0000]
CodeHeap 'non-nmethods': size=5760Kb used=1213Kb max_used=1240Kb free=4546Kb
 bounds [0x000002263a8b0000, 0x000002263ab20000, 0x000002263ae50000]
 total_blobs=2122 nmethods=1644 adapters=385
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 5.001 Thread 0x0000022689448120 nmethod 1620 0x000002263363ed90 code [0x000002263363ef40, 0x000002263363f0f8]
Event: 5.004 Thread 0x0000022630110dd0 1621       4       lombok.patcher.scripts.AddFieldScript::patch (55 bytes)
Event: 5.011 Thread 0x0000022689448120 1624   !   3       java.net.URLClassLoader::findClass (47 bytes)
Event: 5.012 Thread 0x0000022689448120 nmethod 1624 0x000002263363f210 code [0x000002263363f400, 0x000002263363fa50]
Event: 5.012 Thread 0x0000022689448120 1625       3       java.net.URLClassLoader$1::<init> (15 bytes)
Event: 5.012 Thread 0x0000022689448120 nmethod 1625 0x000002263363fc90 code [0x000002263363fe40, 0x000002263363ffd8]
Event: 5.012 Thread 0x0000022689448120 1626   !   3       java.net.URLClassLoader$1::run (81 bytes)
Event: 5.015 Thread 0x0000022689448120 nmethod 1626 0x0000022633640090 code [0x0000022633640440, 0x0000022633641918]
Event: 5.015 Thread 0x0000022689448120 1627   !   3       jdk.internal.reflect.DirectConstructorHandleAccessor::newInstance (130 bytes)
Event: 5.019 Thread 0x0000022689448120 nmethod 1627 0x0000022633642190 code [0x0000022633642680, 0x0000022633644b20]
Event: 5.019 Thread 0x0000022689448120 1628       3       jdk.internal.reflect.DirectConstructorHandleAccessor::invokeImpl (103 bytes)
Event: 5.021 Thread 0x0000022689448120 nmethod 1628 0x0000022633645e90 code [0x00000226336461e0, 0x0000022633647918]
Event: 5.021 Thread 0x0000022689448120 1630       3       java.lang.String::getBytesNoRepl1 (89 bytes)
Event: 5.023 Thread 0x0000022689448120 nmethod 1630 0x0000022633647f90 code [0x0000022633648220, 0x0000022633648b38]
Event: 5.023 Thread 0x0000022689448120 1631       3       java.lang.String::isASCII (16 bytes)
Event: 5.023 Thread 0x0000022689448120 nmethod 1631 0x0000022633648e10 code [0x0000022633648fc0, 0x00000226336491f0]
Event: 5.023 Thread 0x0000022689448120 1632       3       java.util.Arrays::mismatch (69 bytes)
Event: 5.024 Thread 0x0000022689448120 nmethod 1632 0x0000022633649310 code [0x00000226336494e0, 0x0000022633649808]
Event: 5.024 Thread 0x0000022689448120 1629       3       java.net.URLClassLoader::getAndVerifyPackage (109 bytes)
Event: 7.043 Thread 0x0000022689448120 nmethod 1629 0x0000022633649990 code [0x0000022633649ce0, 0x000002263364ad88]

GC Heap History (2 events):
Event: 2.770 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 25600K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 0K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080000000,0x0000000084300000)
 Metaspace       used 4315K, committed 4544K, reserved 1114112K
  class space    used 458K, committed 576K, reserved 1048576K
}
Event: 2.784 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 3335K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 81% used [0x00000000d6e80000,0x00000000d71c1c68,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 16K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080004000,0x0000000084300000)
 Metaspace       used 4315K, committed 4544K, reserved 1114112K
  class space    used 458K, committed 576K, reserved 1048576K
}

Dll operation events (8 events):
Event: 0.029 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.377 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.524 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.535 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.543 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.560 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.622 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.830 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

Deoptimization events (20 events):
Event: 3.372 Thread 0x000002262dd60390 DEOPT PACKING pc=0x000002263aeab8c4 sp=0x00000072dd1f8880
Event: 3.372 Thread 0x000002262dd60390 DEOPT UNPACKING pc=0x000002263a903aa2 sp=0x00000072dd1f87e8 mode 2
Event: 3.459 Thread 0x000002262dd60390 DEOPT PACKING pc=0x000002263341df0d sp=0x00000072dd1f7c90
Event: 3.459 Thread 0x000002262dd60390 DEOPT UNPACKING pc=0x000002263a904242 sp=0x00000072dd1f7140 mode 0
Event: 3.667 Thread 0x000002262dd60390 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000002263ae8bf04 relative=0x00000000000002c4
Event: 3.667 Thread 0x000002262dd60390 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000002263ae8bf04 method=java.util.Collections$UnmodifiableCollection$1.next()Ljava/lang/Object; @ 4 c2
Event: 3.667 Thread 0x000002262dd60390 DEOPT PACKING pc=0x000002263ae8bf04 sp=0x00000072dd1f8320
Event: 3.667 Thread 0x000002262dd60390 DEOPT UNPACKING pc=0x000002263a903aa2 sp=0x00000072dd1f82d0 mode 2
Event: 3.667 Thread 0x000002262dd60390 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000002263ae8bf04 relative=0x00000000000002c4
Event: 3.667 Thread 0x000002262dd60390 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000002263ae8bf04 method=java.util.Collections$UnmodifiableCollection$1.next()Ljava/lang/Object; @ 4 c2
Event: 3.667 Thread 0x000002262dd60390 DEOPT PACKING pc=0x000002263ae8bf04 sp=0x00000072dd1f8320
Event: 3.667 Thread 0x000002262dd60390 DEOPT UNPACKING pc=0x000002263a903aa2 sp=0x00000072dd1f82d0 mode 2
Event: 4.949 Thread 0x000002262dd60390 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002263aed2dd0 relative=0x00000000000000b0
Event: 4.949 Thread 0x000002262dd60390 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002263aed2dd0 method=java.util.concurrent.locks.ReentrantLock$NonfairSync.initialTryLock()Z @ 10 c2
Event: 4.949 Thread 0x000002262dd60390 DEOPT PACKING pc=0x000002263aed2dd0 sp=0x00000072dd1fe7d0
Event: 4.949 Thread 0x000002262dd60390 DEOPT UNPACKING pc=0x000002263a903aa2 sp=0x00000072dd1fe760 mode 2
Event: 4.950 Thread 0x000002262dd60390 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002263aed1634 relative=0x0000000000000094
Event: 4.950 Thread 0x000002262dd60390 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002263aed1634 method=java.util.concurrent.locks.ReentrantLock$Sync.tryRelease(I)Z @ 26 c2
Event: 4.950 Thread 0x000002262dd60390 DEOPT PACKING pc=0x000002263aed1634 sp=0x00000072dd1fe8a0
Event: 4.951 Thread 0x000002262dd60390 DEOPT UNPACKING pc=0x000002263a903aa2 sp=0x00000072dd1fe820 mode 2

Classes loaded (20 events):
Event: 5.003 Loading class java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue
Event: 5.004 Loading class java/util/concurrent/ScheduledThreadPoolExecutor$DelayedWorkQueue done
Event: 5.004 Loading class java/util/concurrent/RunnableScheduledFuture
Event: 5.004 Loading class java/util/concurrent/RunnableFuture
Event: 5.004 Loading class java/util/concurrent/RunnableFuture done
Event: 5.004 Loading class java/util/concurrent/ScheduledFuture
Event: 5.005 Loading class java/util/concurrent/Delayed
Event: 5.005 Loading class java/util/concurrent/Delayed done
Event: 5.005 Loading class java/util/concurrent/ScheduledFuture done
Event: 5.005 Loading class java/util/concurrent/RunnableScheduledFuture done
Event: 5.007 Loading class java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask
Event: 5.007 Loading class java/util/concurrent/FutureTask
Event: 5.007 Loading class java/util/concurrent/FutureTask done
Event: 5.008 Loading class java/util/concurrent/ScheduledThreadPoolExecutor$ScheduledFutureTask done
Event: 5.008 Loading class java/util/concurrent/FutureTask$WaitNode
Event: 5.008 Loading class java/util/concurrent/FutureTask$WaitNode done
Event: 5.008 Loading class java/util/concurrent/Executors$RunnableAdapter
Event: 5.008 Loading class java/util/concurrent/Executors$RunnableAdapter done
Event: 5.009 Loading class java/util/concurrent/ThreadPoolExecutor$Worker
Event: 5.009 Loading class java/util/concurrent/ThreadPoolExecutor$Worker done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 2.208 Thread 0x000002262dd60390 Exception <a 'java/io/FileNotFoundException'{0x00000000d64fdcf8}> (0x00000000d64fdcf8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 2.232 Thread 0x000002262dd60390 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6536250}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x00000000d6536250) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.244 Thread 0x000002262dd60390 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6549ac0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d6549ac0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.268 Thread 0x000002262dd60390 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6560d18}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d6560d18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.365 Thread 0x000002262dd60390 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d65f47a8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x00000000d65f47a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.619 Thread 0x000002262dd60390 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6a6cf40}: 'java.lang.ClassLoader java.lang.ClassLoader.getPlatformClassLoader(java.lang.Class)'> (0x00000000d6a6cf40) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.799 Thread 0x000002262dd60390 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d5588560}: sun/net/www/protocol/c/Handler> (0x00000000d5588560) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 2.800 Thread 0x000002262dd60390 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d5589960}: sun/net/www/protocol/c/Handler> (0x00000000d5589960) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 2.800 Thread 0x000002262dd60390 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d558abe8}: sun/net/www/protocol/c/Handler> (0x00000000d558abe8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 2.823 Thread 0x000002262dd60390 Exception <a 'java/io/FileNotFoundException'{0x00000000d55b3420}> (0x00000000d55b3420) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 2.927 Thread 0x000002262dd60390 Implicit null exception at 0x000002263aeb42e6 to 0x000002263aeb4392
Event: 2.927 Thread 0x000002262dd60390 Implicit null exception at 0x000002263aec27a0 to 0x000002263aec29bc
Event: 2.940 Thread 0x000002262dd60390 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d57a0710}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d57a0710) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.944 Thread 0x000002262dd60390 Implicit null exception at 0x000002263aeb4a40 to 0x000002263aeb4d14
Event: 3.136 Thread 0x000002262dd60390 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a39330}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5a39330) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.892 Thread 0x000002262dd60390 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6427df0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000000d6427df0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.983 Thread 0x000002262dd60390 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d659cf50}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d659cf50) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.986 Thread 0x000002262dd60390 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d65a53d8}: Found class java.lang.Object, but interface was expected> (0x00000000d65a53d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 4.987 Thread 0x000002262dd60390 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d65ab9e8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000d65ab9e8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 4.988 Thread 0x000002262dd60390 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d65b4c20}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000d65b4c20) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 0.370 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.669 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.669 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.670 Executing VM operation: Cleanup
Event: 1.686 Executing VM operation: Cleanup done
Event: 1.951 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.951 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 2.015 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 2.016 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 2.716 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 2.716 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 2.716 Executing VM operation: Cleanup
Event: 2.716 Executing VM operation: Cleanup done
Event: 2.770 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 2.784 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 3.787 Executing VM operation: Cleanup
Event: 3.787 Executing VM operation: Cleanup done
Event: 4.800 Executing VM operation: Cleanup
Event: 4.800 Executing VM operation: Cleanup done
Event: 5.811 Executing VM operation: Cleanup

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.127 Thread 0x000002262dd60390 Thread added: 0x000002263010c6f0
Event: 0.127 Thread 0x000002262dd60390 Thread added: 0x000002263010d140
Event: 0.128 Thread 0x000002262dd60390 Thread added: 0x000002263010ffa0
Event: 0.128 Thread 0x000002262dd60390 Thread added: 0x0000022630110dd0
Event: 0.129 Thread 0x000002262dd60390 Thread added: 0x0000022689448120
Event: 0.231 Thread 0x000002262dd60390 Thread added: 0x0000022630207530
Event: 0.814 Thread 0x0000022630110dd0 Thread added: 0x0000022689738f90
Event: 0.967 Thread 0x0000022689738f90 Thread exited: 0x0000022689738f90
Event: 1.321 Thread 0x000002262dd60390 Thread added: 0x0000022689751fe0
Event: 1.501 Thread 0x0000022630110dd0 Thread added: 0x0000022689762a00
Event: 2.010 Thread 0x0000022630110dd0 Thread added: 0x000002268e09faf0
Event: 2.227 Thread 0x000002268e09faf0 Thread exited: 0x000002268e09faf0
Event: 2.888 Thread 0x0000022689762a00 Thread exited: 0x0000022689762a00
Event: 3.221 Thread 0x0000022689448120 Thread added: 0x000002268e652380
Event: 3.223 Thread 0x000002268e652380 Thread added: 0x000002268e5adb10
Event: 3.560 Thread 0x000002268e5adb10 Thread exited: 0x000002268e5adb10
Event: 4.833 Thread 0x000002268e652380 Thread exited: 0x000002268e652380
Event: 4.847 Thread 0x0000022689448120 Thread added: 0x000002268e652380
Event: 5.009 Thread 0x000002262dd60390 Thread added: 0x000002268e5fc2d0
Event: 5.011 Thread 0x000002268e652380 Thread exited: 0x000002268e652380


Dynamic libraries:
0x00007ff675db0000 - 0x00007ff675dbe000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007fff7f780000 - 0x00007fff7f9e5000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fff7ebe0000 - 0x00007fff7eca9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fff7ccf0000 - 0x00007fff7d0d8000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fff7d340000 - 0x00007fff7d48b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff6f490000 - 0x00007fff6f4ae000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007fff6d520000 - 0x00007fff6d538000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007fff7f1a0000 - 0x00007fff7f36a000 	C:\WINDOWS\System32\USER32.dll
0x00007fff660e0000 - 0x00007fff6637a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007fff7d260000 - 0x00007fff7d287000 	C:\WINDOWS\System32\win32u.dll
0x00007fff7ed90000 - 0x00007fff7ee39000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fff7f710000 - 0x00007fff7f73b000 	C:\WINDOWS\System32\GDI32.dll
0x00007fff7c8d0000 - 0x00007fff7ca07000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fff7d290000 - 0x00007fff7d333000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fff7ecd0000 - 0x00007fff7ed00000 	C:\WINDOWS\System32\IMM32.DLL
0x00007fff77dc0000 - 0x00007fff77dcc000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007fff56cc0000 - 0x00007fff56d4d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fff0f800000 - 0x00007fff10590000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007fff7f490000 - 0x00007fff7f543000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fff7f3d0000 - 0x00007fff7f476000 	C:\WINDOWS\System32\sechost.dll
0x00007fff7d520000 - 0x00007fff7d635000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fff7eeb0000 - 0x00007fff7ef24000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fff666f0000 - 0x00007fff66725000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fff7b510000 - 0x00007fff7b56e000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007fff6de40000 - 0x00007fff6de4b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fff7b4f0000 - 0x00007fff7b504000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007fff7b7b0000 - 0x00007fff7b7cb000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fff76400000 - 0x00007fff7640a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007fff76bf0000 - 0x00007fff76e31000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fff7d860000 - 0x00007fff7dbe5000 	C:\WINDOWS\System32\combase.dll
0x00007fff7f0b0000 - 0x00007fff7f191000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fff61080000 - 0x00007fff610b9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fff7ca10000 - 0x00007fff7caa9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fff6ca00000 - 0x00007fff6ca0f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007fff6c920000 - 0x00007fff6c93f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007fff7e140000 - 0x00007fff7e882000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fff7d0e0000 - 0x00007fff7d254000 	C:\WINDOWS\System32\wintypes.dll
0x00007fff7a590000 - 0x00007fff7ade8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fff7f580000 - 0x00007fff7f671000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fff7d640000 - 0x00007fff7d6aa000 	C:\WINDOWS\System32\shlwapi.dll
0x00007fff7c7e0000 - 0x00007fff7c80f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff6c870000 - 0x00007fff6c888000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007fff6f440000 - 0x00007fff6f450000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007fff76ff0000 - 0x00007fff7710e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007fff7bd20000 - 0x00007fff7bd8a000 	C:\WINDOWS\system32\mswsock.dll
0x00007fff6c850000 - 0x00007fff6c866000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007fff6c280000 - 0x00007fff6c290000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\d2cab3d8955a161a36a6f404eeb8145d\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\d2cab3d8955a161a36a6f404eeb8145d\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-3c20df3b44f3691dd5d35870ad860669-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\d2cab3d8955a161a36a6f404eeb8145d\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-11.0.25
PATH=C:\Gradle\gradle-8.13\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\Java\jdk-11.0.25\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\SqlCmd\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64\compiler;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Intel\WiFi\bin\;C:\Program Files\Common Files\Intel\WirelessCommon\;C:\ProgramData\chocolatey\bin;C:\Python312\Scripts;C:\Python312;C:\Program Files\dotnet\;C:\xampp\php\php.exe;C:\php;C:\tools\php84;C:\xampp\mysql\bin;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\ncat;C:\curl;C:\Users\<USER>\AppData\Roaming\npm\node_modules;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Program Files\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundatio;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\Gradle\gradle-8.13\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\Scripts;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files (x86)\Nmap;C:\xampp\mysql\b;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\MongoDB\Server\7.0\bin;C:\Program Files\MongoDB\Server\8.0\bin;;C:\PostgreSQL\16\bin;C:\Program Files\SqlCmd\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64\compiler;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Intel\WiFi\bin\;C:\Program Files\Common Files\Intel\WirelessCommon\;C:\ProgramData\chocolatey\bin;C:\Python312\Scripts;C:\Python312;C:\Program Files\dotnet\;C:\xampp\php\php.exe;C:\php;C:\tools\php84;C:\xampp\mysql\bin;C:\Program Files\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\Scripts;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files (x86)\Nmap;C:\xampp\mysql\b;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\composer;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin
USERNAME=Sheldon
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 10, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 0 days 17:50 hours

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 142 stepping 10 microcode 0xf6, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 2112, Current Mhz: 1910, Mhz Limit: 1900

Memory: 4k page, system-wide physical 16222M (1471M free)
TotalPageFile size 21072M (AvailPageFile size 2M)
current process WorkingSet (physical memory assigned to process): 83M, peak: 87M
current process commit charge ("private bytes"): 234M, peak: 239M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
