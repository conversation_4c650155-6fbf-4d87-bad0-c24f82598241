#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1184496 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (./open/src/hotspot/share/memory/arena.cpp:197), pid=9932, tid=4108
#
# JRE version: Java(TM) SE Runtime Environment 18.9 (11.0.25+9) (build 11.0.25+9-LTS-256)
# Java VM: Java HotSpot(TM) 64-Bit Server VM 18.9 (11.0.25+9-LTS-256, mixed mode, tiered, compressed oops, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=512m -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: Intel(R) Core(TM) i7-8650U CPU @ 1.90GHz, 8 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Tue Jul  1 12:33:13 2025 E. Africa Standard Time elapsed time: 481.693786 seconds (0d 0h 8m 1s)

---------------  T H R E A D  ---------------

Current thread (0x000001d3fab0d800):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=4108, stack(0x00000025bfc00000,0x00000025bfd00000)]


Current CompileTask:
C2: 481693 34127       4       org.gradle.internal.resource.transfer.AbstractProgressLoggingHandler$ProgressLoggingInputStream::read (27 bytes)

Stack: [0x00000025bfc00000,0x00000025bfd00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x65824a]
V  [jvm.dll+0x79ae8f]
V  [jvm.dll+0x79c559]
V  [jvm.dll+0x79cc03]
V  [jvm.dll+0x255785]
V  [jvm.dll+0xb312c]
V  [jvm.dll+0xb373c]
V  [jvm.dll+0x35fa2f]
V  [jvm.dll+0x32c755]
V  [jvm.dll+0x32bb3a]
V  [jvm.dll+0x20f12d]
V  [jvm.dll+0x20e0fd]
V  [jvm.dll+0x18b381]
V  [jvm.dll+0x21e774]
V  [jvm.dll+0x21ca9c]
V  [jvm.dll+0x75ed51]
V  [jvm.dll+0x757674]
V  [jvm.dll+0x6570f5]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001d3fde40780, length=143, elements={
0x000001d3ea93d000, 0x000001d3fa223000, 0x000001d3fa22c000, 0x000001d3fa23d800,
0x000001d3fa23e800, 0x000001d3faac6800, 0x000001d3fab0d800, 0x000001d3fab24000,
0x000001d3fab2d000, 0x000001d3faac0800, 0x000001d3fd2fc000, 0x000001d3fc691800,
0x000001d3fc873800, 0x000001d3fd1aa800, 0x000001d3fc3ea000, 0x000001d3fbdba000,
0x000001d3fb901800, 0x000001d3fb9ac800, 0x000001d3fb9ad800, 0x000001d3fc3af800,
0x000001d3fd3e9000, 0x000001d3fd3e6800, 0x000001d3fd3e9800, 0x000001d3fd3e7000,
0x000001d3fd3ea800, 0x000001d3fd3e8000, 0x000001d3fd3eb800, 0x000001d3fd3ec000,
0x000001d3fd3ed000, 0x000001d3926c7800, 0x000001d3926c5800, 0x000001d3926c7000,
0x000001d3926c6000, 0x000001d3926c9800, 0x000001d3926ca000, 0x000001d3916ea000,
0x000001d3916e7800, 0x000001d3916e9800, 0x000001d3916eb000, 0x000001d3916e8800,
0x000001d3916ec000, 0x000001d3916ee000, 0x000001d3916f2800, 0x000001d3916f1800,
0x000001d3916f0000, 0x000001d3916f3000, 0x000001d3916f0800, 0x000001d3916f5000,
0x000001d3a3064800, 0x000001d3a3063800, 0x000001d3a3067800, 0x000001d3a3066800,
0x000001d3a3065000, 0x000001d3a306b000, 0x000001d3a3068800, 0x000001d3a306a000,
0x000001d3a306b800, 0x000001d3a3069000, 0x000001d3a306f000, 0x000001d3a306d800,
0x000001d3a306e000, 0x000001d3a306c800, 0x000001d3a3072000, 0x000001d3a306f800,
0x000001d3a3071800, 0x000001d3a3070800, 0x000001d38f94e800, 0x000001d38f94e000,
0x000001d38f951000, 0x000001d38f954800, 0x000001d38f952800, 0x000001d38f95a000,
0x000001d38f95b000, 0x000001d391bf9800, 0x000001d391bf7000, 0x000001d391bfa800,
0x000001d391bf8000, 0x000001d391bfd800, 0x000001d391bfc000, 0x000001d391bfc800,
0x000001d391bfb000, 0x000001d391bfe800, 0x000001d38f958800, 0x000001d38f959000,
0x000001d38f95c800, 0x000001d38f95b800, 0x000001d3916f6800, 0x000001d38fea7000,
0x000001d38fea5000, 0x000001d38fea6800, 0x000001d38fea8000, 0x000001d38fea5800,
0x000001d38fea9000, 0x000001d38fea9800, 0x000001d38fead000, 0x000001d38feaa800,
0x000001d38fead800, 0x000001d38feab800, 0x000001d38feae800, 0x000001d38feac000,
0x000001d38feb2000, 0x000001d38feaf800, 0x000001d38feb2800, 0x000001d38feb0000,
0x000001d38feb3800, 0x000001d38feb1000, 0x000001d39102f800, 0x000001d39102d000,
0x000001d391030800, 0x000001d39102e000, 0x000001d39102f000, 0x000001d391032000,
0x000001d391033000, 0x000001d391033800, 0x000001d391031800, 0x000001d391037000,
0x000001d391038000, 0x000001d391038800, 0x000001d391036000, 0x000001d39103a000,
0x000001d391039800, 0x000001d39103b000, 0x000001d39103c000, 0x000001d3916ef000,
0x000001d3a300f000, 0x000001d3a300f800, 0x000001d3a3010800, 0x000001d3a300e000,
0x000001d3a3011000, 0x000001d3a3012000, 0x000001d3a3013000, 0x000001d3a3015800,
0x000001d3a3014800, 0x000001d3a3013800, 0x000001d3a3016000, 0x000001d3a3018000,
0x000001d3a3018800, 0x000001d3a3019800, 0x000001d3a3017000, 0x000001d3a301c800,
0x000001d3a301a000, 0x000001d3a301c000, 0x000001d391bf5800
}

Java Threads: ( => current thread )
  0x000001d3ea93d000 JavaThread "main" [_thread_blocked, id=10612, stack(0x00000025bf000000,0x00000025bf100000)]
  0x000001d3fa223000 JavaThread "Reference Handler" daemon [_thread_blocked, id=1796, stack(0x00000025bf700000,0x00000025bf800000)]
  0x000001d3fa22c000 JavaThread "Finalizer" daemon [_thread_blocked, id=15884, stack(0x00000025bf800000,0x00000025bf900000)]
  0x000001d3fa23d800 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=16212, stack(0x00000025bf900000,0x00000025bfa00000)]
  0x000001d3fa23e800 JavaThread "Attach Listener" daemon [_thread_blocked, id=16088, stack(0x00000025bfa00000,0x00000025bfb00000)]
  0x000001d3faac6800 JavaThread "Service Thread" daemon [_thread_blocked, id=1272, stack(0x00000025bfb00000,0x00000025bfc00000)]
=>0x000001d3fab0d800 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=4108, stack(0x00000025bfc00000,0x00000025bfd00000)]
  0x000001d3fab24000 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=16916, stack(0x00000025bfd00000,0x00000025bfe00000)]
  0x000001d3fab2d000 JavaThread "Sweeper thread" daemon [_thread_blocked, id=23800, stack(0x00000025bfe00000,0x00000025bff00000)]
  0x000001d3faac0800 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=18520, stack(0x00000025bff00000,0x00000025c0000000)]
  0x000001d3fd2fc000 JavaThread "Daemon health stats" [_thread_blocked, id=20268, stack(0x00000025c0200000,0x00000025c0300000)]
  0x000001d3fc691800 JavaThread "Incoming local TCP Connector on port 36187" [_thread_in_native, id=26244, stack(0x00000025c0000000,0x00000025c0100000)]
  0x000001d3fc873800 JavaThread "Daemon periodic checks" [_thread_blocked, id=26248, stack(0x00000025c0900000,0x00000025c0a00000)]
  0x000001d3fd1aa800 JavaThread "Daemon" [_thread_blocked, id=26224, stack(0x00000025c0a00000,0x00000025c0b00000)]
  0x000001d3fc3ea000 JavaThread "Handler for socket connection from /127.0.0.1:36187 to /127.0.0.1:36189" [_thread_in_native, id=6108, stack(0x00000025c0b00000,0x00000025c0c00000)]
  0x000001d3fbdba000 JavaThread "Cancel handler" [_thread_blocked, id=17904, stack(0x00000025c0c00000,0x00000025c0d00000)]
  0x000001d3fb901800 JavaThread "Daemon worker" [_thread_blocked, id=8676, stack(0x00000025c0d00000,0x00000025c0e00000)]
  0x000001d3fb9ac800 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:36187 to /127.0.0.1:36189" [_thread_blocked, id=24540, stack(0x00000025c0e00000,0x00000025c0f00000)]
  0x000001d3fb9ad800 JavaThread "Stdin handler" [_thread_blocked, id=10448, stack(0x00000025c0f00000,0x00000025c1000000)]
  0x000001d3fc3af800 JavaThread "Daemon client event forwarder" [_thread_blocked, id=9964, stack(0x00000025c1000000,0x00000025c1100000)]
  0x000001d3fd3e9000 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=25416, stack(0x00000025c1400000,0x00000025c1500000)]
  0x000001d3fd3e6800 JavaThread "File lock request listener" [_thread_in_native, id=20388, stack(0x00000025c1500000,0x00000025c1600000)]
  0x000001d3fd3e9800 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)" [_thread_blocked, id=25092, stack(0x00000025c1600000,0x00000025c1700000)]
  0x000001d3fd3e7000 JavaThread "Problems report writer" [_thread_blocked, id=2792, stack(0x00000025c1800000,0x00000025c1900000)]
  0x000001d3fd3ea800 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\android\.gradle\8.12\fileHashes)" [_thread_blocked, id=22812, stack(0x00000025c1900000,0x00000025c1a00000)]
  0x000001d3fd3e8000 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=15920, stack(0x00000025c1a00000,0x00000025c1b00000)]
  0x000001d3fd3eb800 JavaThread "File watcher server" daemon [_thread_in_native, id=24164, stack(0x00000025c1b00000,0x00000025c1c00000)]
  0x000001d3fd3ec000 JavaThread "File watcher consumer" daemon [_thread_blocked, id=1360, stack(0x00000025c1c00000,0x00000025c1d00000)]
  0x000001d3fd3ed000 JavaThread "jar transforms" [_thread_blocked, id=10840, stack(0x00000025c1d00000,0x00000025c1e00000)]
  0x000001d3926c7800 JavaThread "jar transforms Thread 2" [_thread_blocked, id=16072, stack(0x00000025c1e00000,0x00000025c1f00000)]
  0x000001d3926c5800 JavaThread "jar transforms Thread 3" [_thread_blocked, id=4828, stack(0x00000025c1f00000,0x00000025c2000000)]
  0x000001d3926c7000 JavaThread "jar transforms Thread 4" [_thread_blocked, id=24172, stack(0x00000025c2000000,0x00000025c2100000)]
  0x000001d3926c6000 JavaThread "jar transforms Thread 5" [_thread_blocked, id=5036, stack(0x00000025c2100000,0x00000025c2200000)]
  0x000001d3926c9800 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\android\.gradle\8.12\checksums)" [_thread_blocked, id=9900, stack(0x00000025c2200000,0x00000025c2300000)]
  0x000001d3926ca000 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)" [_thread_blocked, id=18168, stack(0x00000025c2300000,0x00000025c2400000)]
  0x000001d3916ea000 JavaThread "jar transforms Thread 6" [_thread_blocked, id=9948, stack(0x00000025c2400000,0x00000025c2500000)]
  0x000001d3916e7800 JavaThread "jar transforms Thread 7" [_thread_blocked, id=25364, stack(0x00000025c2500000,0x00000025c2600000)]
  0x000001d3916e9800 JavaThread "jar transforms Thread 8" [_thread_blocked, id=24752, stack(0x00000025c2600000,0x00000025c2700000)]
  0x000001d3916eb000 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.12\md-rule)" [_thread_blocked, id=23140, stack(0x00000025c2700000,0x00000025c2800000)]
  0x000001d3916e8800 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.12\md-supplier)" [_thread_blocked, id=9940, stack(0x00000025c2800000,0x00000025c2900000)]
  0x000001d3916ec000 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)" [_thread_blocked, id=26404, stack(0x00000025c2900000,0x00000025c2a00000)]
  0x000001d3916ee000 JavaThread "Unconstrained build operations" [_thread_blocked, id=21892, stack(0x00000025c2b00000,0x00000025c2c00000)]
  0x000001d3916f2800 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=12572, stack(0x00000025c2c00000,0x00000025c2d00000)]
  0x000001d3916f1800 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=24120, stack(0x00000025c2d00000,0x00000025c2e00000)]
  0x000001d3916f0000 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=3520, stack(0x00000025c2e00000,0x00000025c2f00000)]
  0x000001d3916f3000 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=21756, stack(0x00000025c2f00000,0x00000025c3000000)]
  0x000001d3916f0800 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=25440, stack(0x00000025c3000000,0x00000025c3100000)]
  0x000001d3916f5000 JavaThread "Kotlin DSL Writer" [_thread_blocked, id=24728, stack(0x00000025bed00000,0x00000025bee00000)]
  0x000001d3a3064800 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=26280, stack(0x00000025c2a00000,0x00000025c2b00000)]
  0x000001d3a3063800 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=20316, stack(0x00000025c3100000,0x00000025c3200000)]
  0x000001d3a3067800 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=15392, stack(0x00000025c3200000,0x00000025c3300000)]
  0x000001d3a3066800 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=1800, stack(0x00000025c3300000,0x00000025c3400000)]
  0x000001d3a3065000 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=9324, stack(0x00000025c3400000,0x00000025c3500000)]
  0x000001d3a306b000 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=12568, stack(0x00000025c3500000,0x00000025c3600000)]
  0x000001d3a3068800 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=26028, stack(0x00000025c3600000,0x00000025c3700000)]
  0x000001d3a306a000 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=3944, stack(0x00000025c3700000,0x00000025c3800000)]
  0x000001d3a306b800 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=4904, stack(0x00000025c3800000,0x00000025c3900000)]
  0x000001d3a3069000 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=17376, stack(0x00000025c3900000,0x00000025c3a00000)]
  0x000001d3a306f000 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=16140, stack(0x00000025c3a00000,0x00000025c3b00000)]
  0x000001d3a306d800 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=15104, stack(0x00000025c3b00000,0x00000025c3c00000)]
  0x000001d3a306e000 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=7060, stack(0x00000025c3c00000,0x00000025c3d00000)]
  0x000001d3a306c800 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=25892, stack(0x00000025c3d00000,0x00000025c3e00000)]
  0x000001d3a3072000 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=8868, stack(0x00000025c3e00000,0x00000025c3f00000)]
  0x000001d3a306f800 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=25592, stack(0x00000025c3f00000,0x00000025c4000000)]
  0x000001d3a3071800 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=25044, stack(0x00000025c4000000,0x00000025c4100000)]
  0x000001d3a3070800 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=20312, stack(0x00000025c4100000,0x00000025c4200000)]
  0x000001d38f94e800 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=26132, stack(0x00000025c4200000,0x00000025c4300000)]
  0x000001d38f94e000 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=4892, stack(0x00000025c4300000,0x00000025c4400000)]
  0x000001d38f951000 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=24864, stack(0x00000025c4400000,0x00000025c4500000)]
  0x000001d38f954800 JavaThread "build event listener" [_thread_blocked, id=6084, stack(0x00000025c4500000,0x00000025c4600000)]
  0x000001d38f952800 JavaThread "Memory manager" [_thread_blocked, id=23968, stack(0x00000025c1700000,0x00000025c1800000)]
  0x000001d38f95a000 JavaThread "ForkJoinPool.commonPool-worker-9" daemon [_thread_blocked, id=17876, stack(0x00000025c4a00000,0x00000025c4b00000)]
  0x000001d38f95b000 JavaThread "ForkJoinPool.commonPool-worker-11" daemon [_thread_blocked, id=25508, stack(0x00000025c4b00000,0x00000025c4c00000)]
  0x000001d391bf9800 JavaThread "Execution worker" [_thread_blocked, id=12964, stack(0x00000025c5200000,0x00000025c5300000)]
  0x000001d391bf7000 JavaThread "Execution worker Thread 2" [_thread_blocked, id=2396, stack(0x00000025c5300000,0x00000025c5400000)]
  0x000001d391bfa800 JavaThread "Execution worker Thread 3" [_thread_blocked, id=12560, stack(0x00000025c5400000,0x00000025c5500000)]
  0x000001d391bf8000 JavaThread "Execution worker Thread 4" [_thread_blocked, id=26092, stack(0x00000025c5500000,0x00000025c5600000)]
  0x000001d391bfd800 JavaThread "Execution worker Thread 5" [_thread_blocked, id=14448, stack(0x00000025c5600000,0x00000025c5700000)]
  0x000001d391bfc000 JavaThread "Execution worker Thread 6" [_thread_blocked, id=14864, stack(0x00000025c5700000,0x00000025c5800000)]
  0x000001d391bfc800 JavaThread "Execution worker Thread 7" [_thread_blocked, id=21676, stack(0x00000025c5800000,0x00000025c5900000)]
  0x000001d391bfb000 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\OneDrive\Desktop\ReactProj\AppNatives\car-app-auth_flow\node_modules\@react-native\gradle-plugin\.gradle\8.12\executionHistory)" [_thread_blocked, id=24932, stack(0x00000025c5900000,0x00000025c5a00000)]
  0x000001d391bfe800 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=23916, stack(0x00000025c5000000,0x00000025c5100000)]
  0x000001d38f958800 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=25860, stack(0x00000025c5a00000,0x00000025c5b00000)]
  0x000001d38f959000 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=25336, stack(0x00000025c4f00000,0x00000025c5000000)]
  0x000001d38f95c800 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=24636, stack(0x00000025c5b00000,0x00000025c5c00000)]
  0x000001d38f95b800 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=6304, stack(0x00000025c5c00000,0x00000025c5d00000)]
  0x000001d3916f6800 JavaThread "Unconstrained build operations Thread 33" [_thread_in_native, id=23816, stack(0x00000025c5d00000,0x00000025c5e00000)]
  0x000001d38fea7000 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=26208, stack(0x00000025c5e00000,0x00000025c5f00000)]
  0x000001d38fea5000 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=24016, stack(0x00000025c5f00000,0x00000025c6000000)]
  0x000001d38fea6800 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=5548, stack(0x00000025c6100000,0x00000025c6200000)]
  0x000001d38fea8000 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=26196, stack(0x00000025c6200000,0x00000025c6300000)]
  0x000001d38fea5800 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=12432, stack(0x00000025c6300000,0x00000025c6400000)]
  0x000001d38fea9000 JavaThread "Unconstrained build operations Thread 39" [_thread_in_native, id=20592, stack(0x00000025c6400000,0x00000025c6500000)]
  0x000001d38fea9800 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=26368, stack(0x00000025c6500000,0x00000025c6600000)]
  0x000001d38fead000 JavaThread "Unconstrained build operations Thread 41" [_thread_blocked, id=25888, stack(0x00000025c6600000,0x00000025c6700000)]
  0x000001d38feaa800 JavaThread "Unconstrained build operations Thread 42" [_thread_blocked, id=18524, stack(0x00000025c6700000,0x00000025c6800000)]
  0x000001d38fead800 JavaThread "Unconstrained build operations Thread 43" [_thread_blocked, id=15612, stack(0x00000025c6000000,0x00000025c6100000)]
  0x000001d38feab800 JavaThread "Unconstrained build operations Thread 44" [_thread_blocked, id=15972, stack(0x00000025c6800000,0x00000025c6900000)]
  0x000001d38feae800 JavaThread "Unconstrained build operations Thread 45" [_thread_blocked, id=11928, stack(0x00000025c6900000,0x00000025c6a00000)]
  0x000001d38feac000 JavaThread "Unconstrained build operations Thread 46" [_thread_blocked, id=12060, stack(0x00000025c6a00000,0x00000025c6b00000)]
  0x000001d38feb2000 JavaThread "Unconstrained build operations Thread 47" [_thread_blocked, id=15056, stack(0x00000025c6b00000,0x00000025c6c00000)]
  0x000001d38feaf800 JavaThread "Unconstrained build operations Thread 48" [_thread_blocked, id=7220, stack(0x00000025c6c00000,0x00000025c6d00000)]
  0x000001d38feb2800 JavaThread "Unconstrained build operations Thread 49" [_thread_blocked, id=13224, stack(0x00000025c6d00000,0x00000025c6e00000)]
  0x000001d38feb0000 JavaThread "Unconstrained build operations Thread 50" [_thread_blocked, id=24072, stack(0x00000025c6e00000,0x00000025c6f00000)]
  0x000001d38feb3800 JavaThread "Unconstrained build operations Thread 51" [_thread_blocked, id=17932, stack(0x00000025c6f00000,0x00000025c7000000)]
  0x000001d38feb1000 JavaThread "Unconstrained build operations Thread 52" [_thread_blocked, id=12396, stack(0x00000025c7000000,0x00000025c7100000)]
  0x000001d39102f800 JavaThread "Unconstrained build operations Thread 53" [_thread_blocked, id=23104, stack(0x00000025c7100000,0x00000025c7200000)]
  0x000001d39102d000 JavaThread "Unconstrained build operations Thread 54" [_thread_blocked, id=4396, stack(0x00000025c7200000,0x00000025c7300000)]
  0x000001d391030800 JavaThread "Unconstrained build operations Thread 55" [_thread_blocked, id=26520, stack(0x00000025c7300000,0x00000025c7400000)]
  0x000001d39102e000 JavaThread "Unconstrained build operations Thread 56" [_thread_blocked, id=14404, stack(0x00000025c7400000,0x00000025c7500000)]
  0x000001d39102f000 JavaThread "Unconstrained build operations Thread 57" [_thread_blocked, id=25448, stack(0x00000025c7500000,0x00000025c7600000)]
  0x000001d391032000 JavaThread "Unconstrained build operations Thread 58" [_thread_blocked, id=20636, stack(0x00000025c7600000,0x00000025c7700000)]
  0x000001d391033000 JavaThread "Unconstrained build operations Thread 59" [_thread_blocked, id=5016, stack(0x00000025c7700000,0x00000025c7800000)]
  0x000001d391033800 JavaThread "Unconstrained build operations Thread 60" [_thread_blocked, id=15940, stack(0x00000025c7800000,0x00000025c7900000)]
  0x000001d391031800 JavaThread "Unconstrained build operations Thread 61" [_thread_blocked, id=25640, stack(0x00000025c7900000,0x00000025c7a00000)]
  0x000001d391037000 JavaThread "Unconstrained build operations Thread 62" [_thread_blocked, id=22692, stack(0x00000025c7a00000,0x00000025c7b00000)]
  0x000001d391038000 JavaThread "WorkerExecutor Queue" [_thread_blocked, id=18896, stack(0x00000025c7d00000,0x00000025c7e00000)]
  0x000001d391038800 JavaThread "RMI Scheduler(0)" daemon [_thread_blocked, id=8012, stack(0x00000025c7b00000,0x00000025c7c00000)]
  0x000001d391036000 JavaThread "RMI RenewClean-[127.0.0.1:17459,org.jetbrains.kotlin.daemon.common.LoopbackNetworkInterface$ClientLoopbackSocketFactory@542de845]" daemon [_thread_blocked, id=25992, stack(0x00000025c7c00000,0x00000025c7d00000)]
  0x000001d39103a000 JavaThread "RMI GC Daemon" daemon [_thread_blocked, id=25492, stack(0x00000025c7e00000,0x00000025c7f00000)]
  0x000001d391039800 JavaThread "RMI TCP Accept-0" daemon [_thread_in_native, id=1868, stack(0x00000025c8000000,0x00000025c8100000)]
  0x000001d39103b000 JavaThread "RMI Reaper" [_thread_blocked, id=26412, stack(0x00000025c8100000,0x00000025c8200000)]
  0x000001d39103c000 JavaThread "RMI TCP Connection(idle)" daemon [_thread_blocked, id=7768, stack(0x00000025c8200000,0x00000025c8300000)]
  0x000001d3916ef000 JavaThread "Unconstrained build operations Thread 63" [_thread_blocked, id=25796, stack(0x00000025c8400000,0x00000025c8500000)]
  0x000001d3a300f000 JavaThread "Unconstrained build operations Thread 64" [_thread_blocked, id=15776, stack(0x00000025c8500000,0x00000025c8600000)]
  0x000001d3a300f800 JavaThread "Unconstrained build operations Thread 65" [_thread_blocked, id=23188, stack(0x00000025c8600000,0x00000025c8700000)]
  0x000001d3a3010800 JavaThread "Unconstrained build operations Thread 66" [_thread_blocked, id=22392, stack(0x00000025c8700000,0x00000025c8800000)]
  0x000001d3a300e000 JavaThread "Unconstrained build operations Thread 67" [_thread_blocked, id=18120, stack(0x00000025c7f00000,0x00000025c8000000)]
  0x000001d3a3011000 JavaThread "Unconstrained build operations Thread 68" [_thread_blocked, id=24472, stack(0x00000025c8300000,0x00000025c8400000)]
  0x000001d3a3012000 JavaThread "Unconstrained build operations Thread 69" [_thread_blocked, id=25760, stack(0x00000025c8800000,0x00000025c8900000)]
  0x000001d3a3013000 JavaThread "Unconstrained build operations Thread 70" [_thread_blocked, id=2268, stack(0x00000025c8900000,0x00000025c8a00000)]
  0x000001d3a3015800 JavaThread "Unconstrained build operations Thread 71" [_thread_blocked, id=21488, stack(0x00000025c8a00000,0x00000025c8b00000)]
  0x000001d3a3014800 JavaThread "Unconstrained build operations Thread 72" [_thread_blocked, id=26376, stack(0x00000025c8b00000,0x00000025c8c00000)]
  0x000001d3a3013800 JavaThread "Unconstrained build operations Thread 73" [_thread_blocked, id=10024, stack(0x00000025c8c00000,0x00000025c8d00000)]
  0x000001d3a3016000 JavaThread "Unconstrained build operations Thread 74" [_thread_blocked, id=16204, stack(0x00000025c8d00000,0x00000025c8e00000)]
  0x000001d3a3018000 JavaThread "Unconstrained build operations Thread 75" [_thread_blocked, id=12256, stack(0x00000025c8e00000,0x00000025c8f00000)]
  0x000001d3a3018800 JavaThread "Unconstrained build operations Thread 76" [_thread_blocked, id=22148, stack(0x00000025c8f00000,0x00000025c9000000)]
  0x000001d3a3019800 JavaThread "Unconstrained build operations Thread 77" [_thread_blocked, id=20400, stack(0x00000025c9000000,0x00000025c9100000)]
  0x000001d3a3017000 JavaThread "Unconstrained build operations Thread 78" [_thread_blocked, id=23268, stack(0x00000025c9100000,0x00000025c9200000)]
  0x000001d3a301c800 JavaThread "Unconstrained build operations Thread 79" [_thread_blocked, id=18336, stack(0x00000025c9200000,0x00000025c9300000)]
  0x000001d3a301a000 JavaThread "Unconstrained build operations Thread 80" [_thread_blocked, id=17000, stack(0x00000025c9300000,0x00000025c9400000)]
  0x000001d3a301c000 JavaThread "WorkerExecutor Queue Thread 2" [_thread_blocked, id=4508, stack(0x00000025c0800000,0x00000025c0900000)]
  0x000001d391bf5800 JavaThread "included builds Thread 2" [_thread_blocked, id=18844, stack(0x00000025bef00000,0x00000025bf000000)]

Other Threads:
  0x000001d3fa21d800 VMThread "VM Thread" [stack: 0x00000025bf600000,0x00000025bf700000] [id=2568]
  0x000001d3fae37800 WatcherThread [stack: 0x00000025c0100000,0x00000025c0200000] [id=23348]
  0x000001d3ea951000 GCTaskThread "GC Thread#0" [stack: 0x00000025bf100000,0x00000025bf200000] [id=26052]
  0x000001d3fb3a9000 GCTaskThread "GC Thread#1" [stack: 0x00000025c0300000,0x00000025c0400000] [id=3200]
  0x000001d3fb49f800 GCTaskThread "GC Thread#2" [stack: 0x00000025c0400000,0x00000025c0500000] [id=25424]
  0x000001d3fb208800 GCTaskThread "GC Thread#3" [stack: 0x00000025c0500000,0x00000025c0600000] [id=23064]
  0x000001d3fb3ea800 GCTaskThread "GC Thread#4" [stack: 0x00000025c0600000,0x00000025c0700000] [id=8432]
  0x000001d3fb3f3800 GCTaskThread "GC Thread#5" [stack: 0x00000025c0700000,0x00000025c0800000] [id=20712]
  0x000001d3fc06a800 GCTaskThread "GC Thread#6" [stack: 0x00000025c1100000,0x00000025c1200000] [id=17360]
  0x000001d3fd23e000 GCTaskThread "GC Thread#7" [stack: 0x00000025c1200000,0x00000025c1300000] [id=21420]
  0x000001d3ea989800 ConcurrentGCThread "G1 Main Marker" [stack: 0x00000025bf200000,0x00000025bf300000] [id=4312]
  0x000001d3ea98f000 ConcurrentGCThread "G1 Conc#0" [stack: 0x00000025bf300000,0x00000025bf400000] [id=9808]
  0x000001d3fd4fd800 ConcurrentGCThread "G1 Conc#1" [stack: 0x00000025c1300000,0x00000025c1400000] [id=12920]
  0x000001d3fa110000 ConcurrentGCThread "G1 Refine#0" [stack: 0x00000025bf400000,0x00000025bf500000] [id=19300]
  0x000001d3fa111000 ConcurrentGCThread "G1 Young RemSet Sampling" [stack: 0x00000025bf500000,0x00000025bf600000] [id=26552]

Threads with active compile tasks:
C2 CompilerThread0   481777 34127       4       org.gradle.internal.resource.transfer.AbstractProgressLoggingHandler$ProgressLoggingInputStream::read (27 bytes)

VM state:not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3
Compressed class space size: 528482304 Address: 0x0000000100000000

Heap:
 garbage-first heap   total 444416K, used 243272K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 18 young (18432K), 12 survivors (12288K)
 Metaspace       used 193135K, capacity 200020K, committed 200240K, reserved 692224K
  class space    used 23215K, capacity 25787K, committed 25800K, reserved 516096K
Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, A=archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%| O|  |TAMS 0x0000000080100000, 0x0000000080000000| Untracked 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%| O|  |TAMS 0x0000000080200000, 0x0000000080100000| Untracked 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HS|  |TAMS 0x0000000080300000, 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%|HC|  |TAMS 0x0000000080400000, 0x0000000080300000| Complete 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%|HC|  |TAMS 0x0000000080500000, 0x0000000080400000| Complete 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%|HS|  |TAMS 0x0000000080600000, 0x0000000080500000| Complete 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080700000, 0x0000000080600000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080800000, 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000, 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000, 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000, 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000, 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080d00000, 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000, 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080f00000, 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000, 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000, 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000, 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081300000, 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081400000, 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081500000, 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081600000, 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081700000, 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081800000, 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081900000, 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081a00000, 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081b00000, 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081c00000, 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081d00000, 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081e00000, 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081f00000, 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000081f00000, 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082100000, 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082200000, 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082300000, 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082400000, 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082500000, 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082600000, 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082700000, 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| O|  |TAMS 0x0000000082800000, 0x0000000082700000| Untracked 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082900000, 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082a00000, 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082b00000, 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082c00000, 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082d00000, 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%| O|  |TAMS 0x0000000082e00000, 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%| O|  |TAMS 0x0000000082f00000, 0x0000000082e00000| Untracked 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%| O|  |TAMS 0x0000000083000000, 0x0000000082f00000| Untracked 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%| O|  |TAMS 0x0000000083100000, 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%| O|  |TAMS 0x0000000083200000, 0x0000000083100000| Untracked 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%|HS|  |TAMS 0x0000000083300000, 0x0000000083200000| Complete 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%|HC|  |TAMS 0x0000000083400000, 0x0000000083300000| Complete 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%|HC|  |TAMS 0x0000000083500000, 0x0000000083400000| Complete 
|  53|0x0000000083500000, 0x0000000083519c00, 0x0000000083600000| 10%| O|  |TAMS 0x0000000083500000, 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%| O|  |TAMS 0x0000000083700000, 0x0000000083600000| Untracked 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%| O|  |TAMS 0x0000000083800000, 0x0000000083700000| Untracked 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%| O|  |TAMS 0x0000000083900000, 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%| O|  |TAMS 0x0000000083a00000, 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%| O|  |TAMS 0x0000000083b00000, 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%| O|  |TAMS 0x0000000083c00000, 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%| O|  |TAMS 0x0000000083d00000, 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083e00000, 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%| O|  |TAMS 0x0000000083f00000, 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000084000000, 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%| O|  |TAMS 0x0000000084100000, 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084200000, 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084300000, 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084400000, 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084500000, 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084600000, 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084700000, 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084800000, 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084900000, 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%| O|  |TAMS 0x0000000084a00000, 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084b00000, 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084c00000, 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084d00000, 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084e00000, 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084f00000, 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000085000000, 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085100000, 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085200000, 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085300000, 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085400000, 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085500000, 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085600000, 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085700000, 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085700000, 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085900000, 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085a00000, 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085b00000, 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085c00000, 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085d00000, 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085e00000, 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085f00000, 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000086000000, 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086100000, 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086200000, 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086200000, 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086300000, 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086400000, 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086500000, 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086600000, 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086700000, 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086900000, 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086a00000, 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086b00000, 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086c00000, 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086d00000, 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086e00000, 0x0000000086d00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086f00000, 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000087000000, 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087100000, 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087200000, 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087300000, 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%|HS|  |TAMS 0x0000000087400000, 0x0000000087300000| Complete 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%|HC|  |TAMS 0x0000000087500000, 0x0000000087400000| Complete 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087600000, 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087700000, 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087800000, 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| O|  |TAMS 0x0000000087900000, 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087a00000, 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| O|  |TAMS 0x0000000087b00000, 0x0000000087a00000| Untracked 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| O|  |TAMS 0x0000000087c00000, 0x0000000087b00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| O|  |TAMS 0x0000000087d00000, 0x0000000087c00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| O|  |TAMS 0x0000000087e00000, 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087f00000, 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| O|  |TAMS 0x0000000088000000, 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%| O|  |TAMS 0x0000000088100000, 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| O|  |TAMS 0x0000000088200000, 0x0000000088100000| Untracked 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%| O|  |TAMS 0x0000000088300000, 0x0000000088200000| Untracked 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%| O|  |TAMS 0x0000000088400000, 0x0000000088300000| Untracked 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%|HS|  |TAMS 0x0000000088500000, 0x0000000088400000| Complete 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%|HC|  |TAMS 0x0000000088600000, 0x0000000088500000| Complete 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%|HC|  |TAMS 0x0000000088700000, 0x0000000088600000| Complete 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%| O|  |TAMS 0x0000000088800000, 0x0000000088700000| Untracked 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%| O|  |TAMS 0x0000000088900000, 0x0000000088800000| Untracked 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| O|  |TAMS 0x0000000088a00000, 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088b00000, 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|  |TAMS 0x0000000088c00000, 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| O|  |TAMS 0x0000000088d00000, 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%| O|  |TAMS 0x0000000088e00000, 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%| O|  |TAMS 0x0000000088f00000, 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%| O|  |TAMS 0x0000000089000000, 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%| O|  |TAMS 0x0000000089100000, 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%| O|  |TAMS 0x0000000089200000, 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%| O|  |TAMS 0x0000000089300000, 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%| O|  |TAMS 0x0000000089400000, 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%| O|  |TAMS 0x0000000089500000, 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%| O|  |TAMS 0x0000000089600000, 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%| O|  |TAMS 0x0000000089700000, 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089700000, 0x0000000089800000|  0%| F|  |TAMS 0x0000000089700000, 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| O|  |TAMS 0x0000000089900000, 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%| O|  |TAMS 0x0000000089a00000, 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089b00000, 0x0000000089b00000|100%| O|  |TAMS 0x0000000089b00000, 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%| O|  |TAMS 0x0000000089c00000, 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| O|  |TAMS 0x0000000089d00000, 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089d00000, 0x0000000089e00000|  0%| F|  |TAMS 0x0000000089d00000, 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%| O|  |TAMS 0x0000000089f00000, 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%| O|  |TAMS 0x000000008a000000, 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%| O|  |TAMS 0x000000008a000000, 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a200000, 0x000000008a200000|100%| O|  |TAMS 0x000000008a200000, 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%| O|  |TAMS 0x000000008a300000, 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a300000, 0x000000008a400000|  0%| F|  |TAMS 0x000000008a300000, 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a500000, 0x000000008a500000|100%| O|  |TAMS 0x000000008a500000, 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%| O|  |TAMS 0x000000008a600000, 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%| O|  |TAMS 0x000000008a700000, 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%| O|  |TAMS 0x000000008a800000, 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%| O|  |TAMS 0x000000008a900000, 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%| O|  |TAMS 0x000000008aa00000, 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%| O|  |TAMS 0x000000008ab00000, 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%|HS|  |TAMS 0x000000008ac00000, 0x000000008ab00000| Complete 
| 172|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%|HC|  |TAMS 0x000000008ad00000, 0x000000008ac00000| Complete 
| 173|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%| O|  |TAMS 0x000000008ae00000, 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008af00000, 0x000000008af00000|100%| O|  |TAMS 0x000000008af00000, 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008b000000, 0x000000008b000000|100%| O|  |TAMS 0x000000008b000000, 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%| O|  |TAMS 0x000000008b100000, 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%| O|  |TAMS 0x000000008b200000, 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b300000, 0x000000008b300000|100%| O|  |TAMS 0x000000008b300000, 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b400000, 0x000000008b400000|100%| O|  |TAMS 0x000000008b400000, 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b500000, 0x000000008b500000|100%| O|  |TAMS 0x000000008b500000, 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b600000, 0x000000008b600000|100%| O|  |TAMS 0x000000008b600000, 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b700000, 0x000000008b700000|100%| O|  |TAMS 0x000000008b700000, 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b800000, 0x000000008b800000|100%| O|  |TAMS 0x000000008b800000, 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b900000, 0x000000008b900000|100%| O|  |TAMS 0x000000008b900000, 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008ba00000, 0x000000008ba00000|100%| O|  |TAMS 0x000000008ba00000, 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008bb00000, 0x000000008bb00000|100%| O|  |TAMS 0x000000008bb00000, 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bc00000, 0x000000008bc00000|100%| O|  |TAMS 0x000000008bc00000, 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bd00000, 0x000000008bd00000|100%| O|  |TAMS 0x000000008bd00000, 0x000000008bc00000| Untracked 
| 189|0x000000008bd00000, 0x000000008be00000, 0x000000008be00000|100%| O|  |TAMS 0x000000008be00000, 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%| O|  |TAMS 0x000000008bf00000, 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008c000000, 0x000000008c000000|100%| O|  |TAMS 0x000000008c000000, 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c100000, 0x000000008c100000|100%| O|  |TAMS 0x000000008c100000, 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c200000, 0x000000008c200000|100%| O|  |TAMS 0x000000008c200000, 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c300000, 0x000000008c300000|100%| O|  |TAMS 0x000000008c200000, 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c400000, 0x000000008c400000|100%| O|  |TAMS 0x000000008c400000, 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c400000, 0x000000008c500000|  0%| F|  |TAMS 0x000000008c400000, 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%| O|  |TAMS 0x000000008c600000, 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%| O|  |TAMS 0x000000008c700000, 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c700000, 0x000000008c800000|  0%| F|  |TAMS 0x000000008c700000, 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c800000, 0x000000008c900000|  0%| F|  |TAMS 0x000000008c800000, 0x000000008c800000| Untracked 
| 201|0x000000008c900000, 0x000000008c900000, 0x000000008ca00000|  0%| F|  |TAMS 0x000000008c900000, 0x000000008c900000| Untracked 
| 202|0x000000008ca00000, 0x000000008ca00000, 0x000000008cb00000|  0%| F|  |TAMS 0x000000008ca00000, 0x000000008ca00000| Untracked 
| 203|0x000000008cb00000, 0x000000008cb00000, 0x000000008cc00000|  0%| F|  |TAMS 0x000000008cb00000, 0x000000008cb00000| Untracked 
| 204|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%| O|  |TAMS 0x000000008cd00000, 0x000000008cc00000| Untracked 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%| O|  |TAMS 0x000000008ce00000, 0x000000008cd00000| Untracked 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%| O|  |TAMS 0x000000008cf00000, 0x000000008ce00000| Untracked 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%| O|  |TAMS 0x000000008d000000, 0x000000008cf00000| Untracked 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| O|  |TAMS 0x000000008d100000, 0x000000008d000000| Untracked 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| O|  |TAMS 0x000000008d200000, 0x000000008d100000| Untracked 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| O|  |TAMS 0x000000008d300000, 0x000000008d200000| Untracked 
| 211|0x000000008d300000, 0x000000008d300000, 0x000000008d400000|  0%| F|  |TAMS 0x000000008d300000, 0x000000008d300000| Untracked 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| O|  |TAMS 0x000000008d400000, 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| O|  |TAMS 0x000000008d500000, 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d700000, 0x000000008d700000|100%| O|  |TAMS 0x000000008d600000, 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d800000, 0x000000008d800000|100%| O|  |TAMS 0x000000008d700000, 0x000000008d700000| Untracked 
| 216|0x000000008d800000, 0x000000008d800000, 0x000000008d900000|  0%| F|  |TAMS 0x000000008d800000, 0x000000008d800000| Untracked 
| 217|0x000000008d900000, 0x000000008d900000, 0x000000008da00000|  0%| F|  |TAMS 0x000000008d900000, 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%| O|  |TAMS 0x000000008db00000, 0x000000008da00000| Untracked 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%| O|  |TAMS 0x000000008dc00000, 0x000000008db00000| Untracked 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%| O|  |TAMS 0x000000008dd00000, 0x000000008dc00000| Untracked 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%| O|  |TAMS 0x000000008de00000, 0x000000008dd00000| Untracked 
| 222|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%| O|  |TAMS 0x000000008df00000, 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008df00000, 0x000000008e000000|  0%| F|  |TAMS 0x000000008df00000, 0x000000008df00000| Untracked 
| 224|0x000000008e000000, 0x000000008e100000, 0x000000008e100000|100%| O|  |TAMS 0x000000008e100000, 0x000000008e000000| Untracked 
| 225|0x000000008e100000, 0x000000008e200000, 0x000000008e200000|100%| O|  |TAMS 0x000000008e200000, 0x000000008e100000| Untracked 
| 226|0x000000008e200000, 0x000000008e300000, 0x000000008e300000|100%| O|  |TAMS 0x000000008e300000, 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e400000, 0x000000008e400000|100%| O|  |TAMS 0x000000008e400000, 0x000000008e300000| Untracked 
| 228|0x000000008e400000, 0x000000008e500000, 0x000000008e500000|100%| O|  |TAMS 0x000000008e500000, 0x000000008e400000| Untracked 
| 229|0x000000008e500000, 0x000000008e600000, 0x000000008e600000|100%| O|  |TAMS 0x000000008e500000, 0x000000008e500000| Untracked 
| 230|0x000000008e600000, 0x000000008e700000, 0x000000008e700000|100%| O|  |TAMS 0x000000008e600000, 0x000000008e600000| Untracked 
| 231|0x000000008e700000, 0x000000008e800000, 0x000000008e800000|100%| O|  |TAMS 0x000000008e700000, 0x000000008e700000| Untracked 
| 232|0x000000008e800000, 0x000000008e900000, 0x000000008e900000|100%| O|  |TAMS 0x000000008e800000, 0x000000008e800000| Untracked 
| 233|0x000000008e900000, 0x000000008ea00000, 0x000000008ea00000|100%| O|  |TAMS 0x000000008e900000, 0x000000008e900000| Untracked 
| 234|0x000000008ea00000, 0x000000008eb00000, 0x000000008eb00000|100%| O|  |TAMS 0x000000008ea00000, 0x000000008ea00000| Untracked 
| 235|0x000000008eb00000, 0x000000008ec00000, 0x000000008ec00000|100%| O|  |TAMS 0x000000008eb00000, 0x000000008eb00000| Untracked 
| 236|0x000000008ec00000, 0x000000008ec00000, 0x000000008ed00000|  0%| F|  |TAMS 0x000000008ec00000, 0x000000008ec00000| Untracked 
| 237|0x000000008ed00000, 0x000000008ed00000, 0x000000008ee00000|  0%| F|  |TAMS 0x000000008ed00000, 0x000000008ed00000| Untracked 
| 238|0x000000008ee00000, 0x000000008ee00000, 0x000000008ef00000|  0%| F|  |TAMS 0x000000008ee00000, 0x000000008ee00000| Untracked 
| 239|0x000000008ef00000, 0x000000008ef00000, 0x000000008f000000|  0%| F|  |TAMS 0x000000008ef00000, 0x000000008ef00000| Untracked 
| 240|0x000000008f000000, 0x000000008f000000, 0x000000008f100000|  0%| F|  |TAMS 0x000000008f000000, 0x000000008f000000| Untracked 
| 241|0x000000008f100000, 0x000000008f100000, 0x000000008f200000|  0%| F|  |TAMS 0x000000008f100000, 0x000000008f100000| Untracked 
| 242|0x000000008f200000, 0x000000008f200000, 0x000000008f300000|  0%| F|  |TAMS 0x000000008f200000, 0x000000008f200000| Untracked 
| 243|0x000000008f300000, 0x000000008f300000, 0x000000008f400000|  0%| F|  |TAMS 0x000000008f300000, 0x000000008f300000| Untracked 
| 244|0x000000008f400000, 0x000000008f400000, 0x000000008f500000|  0%| F|  |TAMS 0x000000008f400000, 0x000000008f400000| Untracked 
| 245|0x000000008f500000, 0x000000008f500000, 0x000000008f600000|  0%| F|  |TAMS 0x000000008f500000, 0x000000008f500000| Untracked 
| 246|0x000000008f600000, 0x000000008f600000, 0x000000008f700000|  0%| F|  |TAMS 0x000000008f600000, 0x000000008f600000| Untracked 
| 247|0x000000008f700000, 0x000000008f700000, 0x000000008f800000|  0%| F|  |TAMS 0x000000008f700000, 0x000000008f700000| Untracked 
| 248|0x000000008f800000, 0x000000008f800000, 0x000000008f900000|  0%| F|  |TAMS 0x000000008f800000, 0x000000008f800000| Untracked 
| 249|0x000000008f900000, 0x000000008f900000, 0x000000008fa00000|  0%| F|  |TAMS 0x000000008f900000, 0x000000008f900000| Untracked 
| 250|0x000000008fa00000, 0x000000008fa00000, 0x000000008fb00000|  0%| F|  |TAMS 0x000000008fa00000, 0x000000008fa00000| Untracked 
| 251|0x000000008fb00000, 0x000000008fb00000, 0x000000008fc00000|  0%| F|  |TAMS 0x000000008fb00000, 0x000000008fb00000| Untracked 
| 252|0x000000008fc00000, 0x000000008fc00000, 0x000000008fd00000|  0%| F|  |TAMS 0x000000008fc00000, 0x000000008fc00000| Untracked 
| 253|0x000000008fd00000, 0x000000008fd00000, 0x000000008fe00000|  0%| F|  |TAMS 0x000000008fd00000, 0x000000008fd00000| Untracked 
| 254|0x000000008fe00000, 0x000000008fe00000, 0x000000008ff00000|  0%| F|  |TAMS 0x000000008fe00000, 0x000000008fe00000| Untracked 
| 255|0x000000008ff00000, 0x000000008ff00000, 0x0000000090000000|  0%| F|  |TAMS 0x000000008ff00000, 0x000000008ff00000| Untracked 
| 256|0x0000000090000000, 0x0000000090000000, 0x0000000090100000|  0%| F|  |TAMS 0x0000000090000000, 0x0000000090000000| Untracked 
| 257|0x0000000090100000, 0x0000000090100000, 0x0000000090200000|  0%| F|  |TAMS 0x0000000090100000, 0x0000000090100000| Untracked 
| 258|0x0000000090200000, 0x0000000090200000, 0x0000000090300000|  0%| F|  |TAMS 0x0000000090200000, 0x0000000090200000| Untracked 
| 259|0x0000000090300000, 0x0000000090300000, 0x0000000090400000|  0%| F|  |TAMS 0x0000000090300000, 0x0000000090300000| Untracked 
| 260|0x0000000090400000, 0x0000000090400000, 0x0000000090500000|  0%| F|  |TAMS 0x0000000090400000, 0x0000000090400000| Untracked 
| 261|0x0000000090500000, 0x0000000090500000, 0x0000000090600000|  0%| F|  |TAMS 0x0000000090500000, 0x0000000090500000| Untracked 
| 262|0x0000000090600000, 0x0000000090600000, 0x0000000090700000|  0%| F|  |TAMS 0x0000000090600000, 0x0000000090600000| Untracked 
| 263|0x0000000090700000, 0x0000000090700000, 0x0000000090800000|  0%| F|  |TAMS 0x0000000090700000, 0x0000000090700000| Untracked 
| 264|0x0000000090800000, 0x0000000090800000, 0x0000000090900000|  0%| F|  |TAMS 0x0000000090800000, 0x0000000090800000| Untracked 
| 265|0x0000000090900000, 0x0000000090900000, 0x0000000090a00000|  0%| F|  |TAMS 0x0000000090900000, 0x0000000090900000| Untracked 
| 266|0x0000000090a00000, 0x0000000090a00000, 0x0000000090b00000|  0%| F|  |TAMS 0x0000000090a00000, 0x0000000090a00000| Untracked 
| 267|0x0000000090b00000, 0x0000000090b00000, 0x0000000090c00000|  0%| F|  |TAMS 0x0000000090b00000, 0x0000000090b00000| Untracked 
| 268|0x0000000090c00000, 0x0000000090c00000, 0x0000000090d00000|  0%| F|  |TAMS 0x0000000090c00000, 0x0000000090c00000| Untracked 
| 269|0x0000000090d00000, 0x0000000090d00000, 0x0000000090e00000|  0%| F|  |TAMS 0x0000000090d00000, 0x0000000090d00000| Untracked 
| 270|0x0000000090e00000, 0x0000000090e00000, 0x0000000090f00000|  0%| F|  |TAMS 0x0000000090e00000, 0x0000000090e00000| Untracked 
| 271|0x0000000090f00000, 0x0000000090f00000, 0x0000000091000000|  0%| F|  |TAMS 0x0000000090f00000, 0x0000000090f00000| Untracked 
| 272|0x0000000091000000, 0x0000000091000000, 0x0000000091100000|  0%| F|  |TAMS 0x0000000091000000, 0x0000000091000000| Untracked 
| 273|0x0000000091100000, 0x0000000091100000, 0x0000000091200000|  0%| F|  |TAMS 0x0000000091100000, 0x0000000091100000| Untracked 
| 274|0x0000000091200000, 0x0000000091200000, 0x0000000091300000|  0%| F|  |TAMS 0x0000000091200000, 0x0000000091200000| Untracked 
| 275|0x0000000091300000, 0x0000000091300000, 0x0000000091400000|  0%| F|  |TAMS 0x0000000091300000, 0x0000000091300000| Untracked 
| 276|0x0000000091400000, 0x0000000091400000, 0x0000000091500000|  0%| F|  |TAMS 0x0000000091400000, 0x0000000091400000| Untracked 
| 277|0x0000000091500000, 0x0000000091578780, 0x0000000091600000| 47%| S|CS|TAMS 0x0000000091500000, 0x0000000091500000| Complete 
| 278|0x0000000091600000, 0x0000000091700000, 0x0000000091700000|100%| S|CS|TAMS 0x0000000091600000, 0x0000000091600000| Complete 
| 279|0x0000000091700000, 0x0000000091800000, 0x0000000091800000|100%| S|CS|TAMS 0x0000000091700000, 0x0000000091700000| Complete 
| 280|0x0000000091800000, 0x0000000091900000, 0x0000000091900000|100%| S|CS|TAMS 0x0000000091800000, 0x0000000091800000| Complete 
| 281|0x0000000091900000, 0x0000000091a00000, 0x0000000091a00000|100%| S|CS|TAMS 0x0000000091900000, 0x0000000091900000| Complete 
| 282|0x0000000091a00000, 0x0000000091b00000, 0x0000000091b00000|100%| S|CS|TAMS 0x0000000091a00000, 0x0000000091a00000| Complete 
| 283|0x0000000091b00000, 0x0000000091c00000, 0x0000000091c00000|100%| S|CS|TAMS 0x0000000091b00000, 0x0000000091b00000| Complete 
| 284|0x0000000091c00000, 0x0000000091d00000, 0x0000000091d00000|100%| S|CS|TAMS 0x0000000091c00000, 0x0000000091c00000| Complete 
| 285|0x0000000091d00000, 0x0000000091e00000, 0x0000000091e00000|100%| S|CS|TAMS 0x0000000091d00000, 0x0000000091d00000| Complete 
| 286|0x0000000091e00000, 0x0000000091f00000, 0x0000000091f00000|100%| S|CS|TAMS 0x0000000091e00000, 0x0000000091e00000| Complete 
| 287|0x0000000091f00000, 0x0000000092000000, 0x0000000092000000|100%| S|CS|TAMS 0x0000000091f00000, 0x0000000091f00000| Complete 
| 288|0x0000000092000000, 0x0000000092100000, 0x0000000092100000|100%| S|CS|TAMS 0x0000000092000000, 0x0000000092000000| Complete 
| 289|0x0000000092100000, 0x0000000092100000, 0x0000000092200000|  0%| F|  |TAMS 0x0000000092100000, 0x0000000092100000| Untracked 
| 290|0x0000000092200000, 0x0000000092200000, 0x0000000092300000|  0%| F|  |TAMS 0x0000000092200000, 0x0000000092200000| Untracked 
| 291|0x0000000092300000, 0x0000000092300000, 0x0000000092400000|  0%| F|  |TAMS 0x0000000092300000, 0x0000000092300000| Untracked 
| 292|0x0000000092400000, 0x0000000092400000, 0x0000000092500000|  0%| F|  |TAMS 0x0000000092400000, 0x0000000092400000| Untracked 
| 293|0x0000000092500000, 0x0000000092500000, 0x0000000092600000|  0%| F|  |TAMS 0x0000000092500000, 0x0000000092500000| Untracked 
| 294|0x0000000092600000, 0x0000000092600000, 0x0000000092700000|  0%| F|  |TAMS 0x0000000092600000, 0x0000000092600000| Untracked 
| 295|0x0000000092700000, 0x0000000092700000, 0x0000000092800000|  0%| F|  |TAMS 0x0000000092700000, 0x0000000092700000| Untracked 
| 296|0x0000000092800000, 0x0000000092800000, 0x0000000092900000|  0%| F|  |TAMS 0x0000000092800000, 0x0000000092800000| Untracked 
| 297|0x0000000092900000, 0x0000000092900000, 0x0000000092a00000|  0%| F|  |TAMS 0x0000000092900000, 0x0000000092900000| Untracked 
| 298|0x0000000092a00000, 0x0000000092a00000, 0x0000000092b00000|  0%| F|  |TAMS 0x0000000092a00000, 0x0000000092a00000| Untracked 
| 299|0x0000000092b00000, 0x0000000092b00000, 0x0000000092c00000|  0%| F|  |TAMS 0x0000000092b00000, 0x0000000092b00000| Untracked 
| 300|0x0000000092c00000, 0x0000000092c00000, 0x0000000092d00000|  0%| F|  |TAMS 0x0000000092c00000, 0x0000000092c00000| Untracked 
| 301|0x0000000092d00000, 0x0000000092d00000, 0x0000000092e00000|  0%| F|  |TAMS 0x0000000092d00000, 0x0000000092d00000| Untracked 
| 302|0x0000000092e00000, 0x0000000092e00000, 0x0000000092f00000|  0%| F|  |TAMS 0x0000000092e00000, 0x0000000092e00000| Untracked 
| 303|0x0000000092f00000, 0x0000000092f00000, 0x0000000093000000|  0%| F|  |TAMS 0x0000000092f00000, 0x0000000092f00000| Untracked 
| 304|0x0000000093000000, 0x0000000093000000, 0x0000000093100000|  0%| F|  |TAMS 0x0000000093000000, 0x0000000093000000| Untracked 
| 305|0x0000000093100000, 0x0000000093100000, 0x0000000093200000|  0%| F|  |TAMS 0x0000000093100000, 0x0000000093100000| Untracked 
| 306|0x0000000093200000, 0x0000000093200000, 0x0000000093300000|  0%| F|  |TAMS 0x0000000093200000, 0x0000000093200000| Untracked 
| 307|0x0000000093300000, 0x0000000093300000, 0x0000000093400000|  0%| F|  |TAMS 0x0000000093300000, 0x0000000093300000| Untracked 
| 308|0x0000000093400000, 0x0000000093400000, 0x0000000093500000|  0%| F|  |TAMS 0x0000000093400000, 0x0000000093400000| Untracked 
| 309|0x0000000093500000, 0x0000000093500000, 0x0000000093600000|  0%| F|  |TAMS 0x0000000093500000, 0x0000000093500000| Untracked 
| 310|0x0000000093600000, 0x0000000093600000, 0x0000000093700000|  0%| F|  |TAMS 0x0000000093600000, 0x0000000093600000| Untracked 
| 311|0x0000000093700000, 0x0000000093700000, 0x0000000093800000|  0%| F|  |TAMS 0x0000000093700000, 0x0000000093700000| Untracked 
| 312|0x0000000093800000, 0x0000000093800000, 0x0000000093900000|  0%| F|  |TAMS 0x0000000093800000, 0x0000000093800000| Untracked 
| 313|0x0000000093900000, 0x0000000093900000, 0x0000000093a00000|  0%| F|  |TAMS 0x0000000093900000, 0x0000000093900000| Untracked 
| 314|0x0000000093a00000, 0x0000000093a00000, 0x0000000093b00000|  0%| F|  |TAMS 0x0000000093a00000, 0x0000000093a00000| Untracked 
| 315|0x0000000093b00000, 0x0000000093b00000, 0x0000000093c00000|  0%| F|  |TAMS 0x0000000093b00000, 0x0000000093b00000| Untracked 
| 316|0x0000000093c00000, 0x0000000093c00000, 0x0000000093d00000|  0%| F|  |TAMS 0x0000000093c00000, 0x0000000093c00000| Untracked 
| 317|0x0000000093d00000, 0x0000000093d00000, 0x0000000093e00000|  0%| F|  |TAMS 0x0000000093d00000, 0x0000000093d00000| Untracked 
| 318|0x0000000093e00000, 0x0000000093e00000, 0x0000000093f00000|  0%| F|  |TAMS 0x0000000093e00000, 0x0000000093e00000| Untracked 
| 319|0x0000000093f00000, 0x0000000093f00000, 0x0000000094000000|  0%| F|  |TAMS 0x0000000093f00000, 0x0000000093f00000| Untracked 
| 320|0x0000000094000000, 0x0000000094000000, 0x0000000094100000|  0%| F|  |TAMS 0x0000000094000000, 0x0000000094000000| Untracked 
| 321|0x0000000094100000, 0x0000000094100000, 0x0000000094200000|  0%| F|  |TAMS 0x0000000094100000, 0x0000000094100000| Untracked 
| 322|0x0000000094200000, 0x0000000094200000, 0x0000000094300000|  0%| F|  |TAMS 0x0000000094200000, 0x0000000094200000| Untracked 
| 323|0x0000000094300000, 0x0000000094300000, 0x0000000094400000|  0%| F|  |TAMS 0x0000000094300000, 0x0000000094300000| Untracked 
| 324|0x0000000094400000, 0x0000000094400000, 0x0000000094500000|  0%| F|  |TAMS 0x0000000094400000, 0x0000000094400000| Untracked 
| 325|0x0000000094500000, 0x0000000094500000, 0x0000000094600000|  0%| F|  |TAMS 0x0000000094500000, 0x0000000094500000| Untracked 
| 326|0x0000000094600000, 0x0000000094600000, 0x0000000094700000|  0%| F|  |TAMS 0x0000000094600000, 0x0000000094600000| Untracked 
| 327|0x0000000094700000, 0x0000000094700000, 0x0000000094800000|  0%| F|  |TAMS 0x0000000094700000, 0x0000000094700000| Untracked 
| 328|0x0000000094800000, 0x0000000094800000, 0x0000000094900000|  0%| F|  |TAMS 0x0000000094800000, 0x0000000094800000| Untracked 
| 329|0x0000000094900000, 0x0000000094900000, 0x0000000094a00000|  0%| F|  |TAMS 0x0000000094900000, 0x0000000094900000| Untracked 
| 330|0x0000000094a00000, 0x0000000094a00000, 0x0000000094b00000|  0%| F|  |TAMS 0x0000000094a00000, 0x0000000094a00000| Untracked 
| 331|0x0000000094b00000, 0x0000000094b00000, 0x0000000094c00000|  0%| F|  |TAMS 0x0000000094b00000, 0x0000000094b00000| Untracked 
| 332|0x0000000094c00000, 0x0000000094c00000, 0x0000000094d00000|  0%| F|  |TAMS 0x0000000094c00000, 0x0000000094c00000| Untracked 
| 333|0x0000000094d00000, 0x0000000094d00000, 0x0000000094e00000|  0%| F|  |TAMS 0x0000000094d00000, 0x0000000094d00000| Untracked 
| 334|0x0000000094e00000, 0x0000000094e00000, 0x0000000094f00000|  0%| F|  |TAMS 0x0000000094e00000, 0x0000000094e00000| Untracked 
| 335|0x0000000094f00000, 0x0000000094f00000, 0x0000000095000000|  0%| F|  |TAMS 0x0000000094f00000, 0x0000000094f00000| Untracked 
| 336|0x0000000095000000, 0x0000000095000000, 0x0000000095100000|  0%| F|  |TAMS 0x0000000095000000, 0x0000000095000000| Untracked 
| 337|0x0000000095100000, 0x0000000095100000, 0x0000000095200000|  0%| F|  |TAMS 0x0000000095100000, 0x0000000095100000| Untracked 
| 338|0x0000000095200000, 0x0000000095200000, 0x0000000095300000|  0%| F|  |TAMS 0x0000000095200000, 0x0000000095200000| Untracked 
| 339|0x0000000095300000, 0x0000000095300000, 0x0000000095400000|  0%| F|  |TAMS 0x0000000095300000, 0x0000000095300000| Untracked 
| 340|0x0000000095400000, 0x0000000095400000, 0x0000000095500000|  0%| F|  |TAMS 0x0000000095400000, 0x0000000095400000| Untracked 
| 341|0x0000000095500000, 0x0000000095500000, 0x0000000095600000|  0%| F|  |TAMS 0x0000000095500000, 0x0000000095500000| Untracked 
| 342|0x0000000095600000, 0x0000000095600000, 0x0000000095700000|  0%| F|  |TAMS 0x0000000095600000, 0x0000000095600000| Untracked 
| 343|0x0000000095700000, 0x0000000095700000, 0x0000000095800000|  0%| F|  |TAMS 0x0000000095700000, 0x0000000095700000| Untracked 
| 344|0x0000000095800000, 0x0000000095800000, 0x0000000095900000|  0%| F|  |TAMS 0x0000000095800000, 0x0000000095800000| Untracked 
| 345|0x0000000095900000, 0x0000000095900000, 0x0000000095a00000|  0%| F|  |TAMS 0x0000000095900000, 0x0000000095900000| Untracked 
| 346|0x0000000095a00000, 0x0000000095a00000, 0x0000000095b00000|  0%| F|  |TAMS 0x0000000095a00000, 0x0000000095a00000| Untracked 
| 347|0x0000000095b00000, 0x0000000095b00000, 0x0000000095c00000|  0%| F|  |TAMS 0x0000000095b00000, 0x0000000095b00000| Untracked 
| 348|0x0000000095c00000, 0x0000000095c00000, 0x0000000095d00000|  0%| F|  |TAMS 0x0000000095c00000, 0x0000000095c00000| Untracked 
| 349|0x0000000095d00000, 0x0000000095d00000, 0x0000000095e00000|  0%| F|  |TAMS 0x0000000095d00000, 0x0000000095d00000| Untracked 
| 350|0x0000000095e00000, 0x0000000095e00000, 0x0000000095f00000|  0%| F|  |TAMS 0x0000000095e00000, 0x0000000095e00000| Untracked 
| 351|0x0000000095f00000, 0x0000000095f00000, 0x0000000096000000|  0%| F|  |TAMS 0x0000000095f00000, 0x0000000095f00000| Untracked 
| 352|0x0000000096000000, 0x0000000096000000, 0x0000000096100000|  0%| F|  |TAMS 0x0000000096000000, 0x0000000096000000| Untracked 
| 353|0x0000000096100000, 0x0000000096100000, 0x0000000096200000|  0%| F|  |TAMS 0x0000000096100000, 0x0000000096100000| Untracked 
| 354|0x0000000096200000, 0x0000000096200000, 0x0000000096300000|  0%| F|  |TAMS 0x0000000096200000, 0x0000000096200000| Untracked 
| 355|0x0000000096300000, 0x0000000096300000, 0x0000000096400000|  0%| F|  |TAMS 0x0000000096300000, 0x0000000096300000| Untracked 
| 356|0x0000000096400000, 0x0000000096400000, 0x0000000096500000|  0%| F|  |TAMS 0x0000000096400000, 0x0000000096400000| Untracked 
| 357|0x0000000096500000, 0x0000000096500000, 0x0000000096600000|  0%| F|  |TAMS 0x0000000096500000, 0x0000000096500000| Untracked 
| 358|0x0000000096600000, 0x0000000096600000, 0x0000000096700000|  0%| F|  |TAMS 0x0000000096600000, 0x0000000096600000| Untracked 
| 359|0x0000000096700000, 0x0000000096700000, 0x0000000096800000|  0%| F|  |TAMS 0x0000000096700000, 0x0000000096700000| Untracked 
| 360|0x0000000096800000, 0x0000000096800000, 0x0000000096900000|  0%| F|  |TAMS 0x0000000096800000, 0x0000000096800000| Untracked 
| 361|0x0000000096900000, 0x0000000096900000, 0x0000000096a00000|  0%| F|  |TAMS 0x0000000096900000, 0x0000000096900000| Untracked 
| 362|0x0000000096a00000, 0x0000000096a00000, 0x0000000096b00000|  0%| F|  |TAMS 0x0000000096a00000, 0x0000000096a00000| Untracked 
| 363|0x0000000096b00000, 0x0000000096b00000, 0x0000000096c00000|  0%| F|  |TAMS 0x0000000096b00000, 0x0000000096b00000| Untracked 
| 364|0x0000000096c00000, 0x0000000096c00000, 0x0000000096d00000|  0%| F|  |TAMS 0x0000000096c00000, 0x0000000096c00000| Untracked 
| 365|0x0000000096d00000, 0x0000000096d00000, 0x0000000096e00000|  0%| F|  |TAMS 0x0000000096d00000, 0x0000000096d00000| Untracked 
| 366|0x0000000096e00000, 0x0000000096e00000, 0x0000000096f00000|  0%| F|  |TAMS 0x0000000096e00000, 0x0000000096e00000| Untracked 
| 367|0x0000000096f00000, 0x0000000096f00000, 0x0000000097000000|  0%| F|  |TAMS 0x0000000096f00000, 0x0000000096f00000| Untracked 
| 368|0x0000000097000000, 0x0000000097000000, 0x0000000097100000|  0%| F|  |TAMS 0x0000000097000000, 0x0000000097000000| Untracked 
| 369|0x0000000097100000, 0x0000000097100000, 0x0000000097200000|  0%| F|  |TAMS 0x0000000097100000, 0x0000000097100000| Untracked 
| 370|0x0000000097200000, 0x0000000097200000, 0x0000000097300000|  0%| F|  |TAMS 0x0000000097200000, 0x0000000097200000| Untracked 
| 371|0x0000000097300000, 0x0000000097300000, 0x0000000097400000|  0%| F|  |TAMS 0x0000000097300000, 0x0000000097300000| Untracked 
| 372|0x0000000097400000, 0x0000000097400000, 0x0000000097500000|  0%| F|  |TAMS 0x0000000097400000, 0x0000000097400000| Untracked 
| 373|0x0000000097500000, 0x0000000097500000, 0x0000000097600000|  0%| F|  |TAMS 0x0000000097500000, 0x0000000097500000| Untracked 
| 374|0x0000000097600000, 0x0000000097600000, 0x0000000097700000|  0%| F|  |TAMS 0x0000000097600000, 0x0000000097600000| Untracked 
| 375|0x0000000097700000, 0x0000000097700000, 0x0000000097800000|  0%| F|  |TAMS 0x0000000097700000, 0x0000000097700000| Untracked 
| 376|0x0000000097800000, 0x0000000097800000, 0x0000000097900000|  0%| F|  |TAMS 0x0000000097800000, 0x0000000097800000| Untracked 
| 377|0x0000000097900000, 0x0000000097900000, 0x0000000097a00000|  0%| F|  |TAMS 0x0000000097900000, 0x0000000097900000| Untracked 
| 378|0x0000000097a00000, 0x0000000097a00000, 0x0000000097b00000|  0%| F|  |TAMS 0x0000000097a00000, 0x0000000097a00000| Untracked 
| 379|0x0000000097b00000, 0x0000000097b00000, 0x0000000097c00000|  0%| F|  |TAMS 0x0000000097b00000, 0x0000000097b00000| Untracked 
| 380|0x0000000097c00000, 0x0000000097c00000, 0x0000000097d00000|  0%| F|  |TAMS 0x0000000097c00000, 0x0000000097c00000| Untracked 
| 381|0x0000000097d00000, 0x0000000097d00000, 0x0000000097e00000|  0%| F|  |TAMS 0x0000000097d00000, 0x0000000097d00000| Untracked 
| 382|0x0000000097e00000, 0x0000000097e00000, 0x0000000097f00000|  0%| F|  |TAMS 0x0000000097e00000, 0x0000000097e00000| Untracked 
| 383|0x0000000097f00000, 0x0000000097f00000, 0x0000000098000000|  0%| F|  |TAMS 0x0000000097f00000, 0x0000000097f00000| Untracked 
| 384|0x0000000098000000, 0x0000000098000000, 0x0000000098100000|  0%| F|  |TAMS 0x0000000098000000, 0x0000000098000000| Untracked 
| 385|0x0000000098100000, 0x0000000098100000, 0x0000000098200000|  0%| F|  |TAMS 0x0000000098100000, 0x0000000098100000| Untracked 
| 386|0x0000000098200000, 0x0000000098200000, 0x0000000098300000|  0%| F|  |TAMS 0x0000000098200000, 0x0000000098200000| Untracked 
| 387|0x0000000098300000, 0x0000000098300000, 0x0000000098400000|  0%| F|  |TAMS 0x0000000098300000, 0x0000000098300000| Untracked 
| 388|0x0000000098400000, 0x0000000098400000, 0x0000000098500000|  0%| F|  |TAMS 0x0000000098400000, 0x0000000098400000| Untracked 
| 389|0x0000000098500000, 0x0000000098500000, 0x0000000098600000|  0%| F|  |TAMS 0x0000000098500000, 0x0000000098500000| Untracked 
| 390|0x0000000098600000, 0x0000000098600000, 0x0000000098700000|  0%| F|  |TAMS 0x0000000098600000, 0x0000000098600000| Untracked 
| 391|0x0000000098700000, 0x0000000098700000, 0x0000000098800000|  0%| F|  |TAMS 0x0000000098700000, 0x0000000098700000| Untracked 
| 392|0x0000000098800000, 0x0000000098800000, 0x0000000098900000|  0%| F|  |TAMS 0x0000000098800000, 0x0000000098800000| Untracked 
| 393|0x0000000098900000, 0x0000000098900000, 0x0000000098a00000|  0%| F|  |TAMS 0x0000000098900000, 0x0000000098900000| Untracked 
| 394|0x0000000098a00000, 0x0000000098a00000, 0x0000000098b00000|  0%| F|  |TAMS 0x0000000098a00000, 0x0000000098a00000| Untracked 
| 395|0x0000000098b00000, 0x0000000098b00000, 0x0000000098c00000|  0%| F|  |TAMS 0x0000000098b00000, 0x0000000098b00000| Untracked 
| 396|0x0000000098c00000, 0x0000000098c00000, 0x0000000098d00000|  0%| F|  |TAMS 0x0000000098c00000, 0x0000000098c00000| Untracked 
| 397|0x0000000098d00000, 0x0000000098d00000, 0x0000000098e00000|  0%| F|  |TAMS 0x0000000098d00000, 0x0000000098d00000| Untracked 
| 398|0x0000000098e00000, 0x0000000098e00000, 0x0000000098f00000|  0%| F|  |TAMS 0x0000000098e00000, 0x0000000098e00000| Untracked 
| 399|0x0000000098f00000, 0x0000000098f00000, 0x0000000099000000|  0%| F|  |TAMS 0x0000000098f00000, 0x0000000098f00000| Untracked 
| 400|0x0000000099000000, 0x0000000099000000, 0x0000000099100000|  0%| F|  |TAMS 0x0000000099000000, 0x0000000099000000| Untracked 
| 401|0x0000000099100000, 0x0000000099100000, 0x0000000099200000|  0%| F|  |TAMS 0x0000000099100000, 0x0000000099100000| Untracked 
| 402|0x0000000099200000, 0x0000000099200000, 0x0000000099300000|  0%| F|  |TAMS 0x0000000099200000, 0x0000000099200000| Untracked 
| 403|0x0000000099300000, 0x0000000099300000, 0x0000000099400000|  0%| F|  |TAMS 0x0000000099300000, 0x0000000099300000| Untracked 
| 404|0x0000000099400000, 0x0000000099400000, 0x0000000099500000|  0%| F|  |TAMS 0x0000000099400000, 0x0000000099400000| Untracked 
| 405|0x0000000099500000, 0x0000000099500000, 0x0000000099600000|  0%| F|  |TAMS 0x0000000099500000, 0x0000000099500000| Untracked 
| 406|0x0000000099600000, 0x0000000099600000, 0x0000000099700000|  0%| F|  |TAMS 0x0000000099600000, 0x0000000099600000| Untracked 
| 407|0x0000000099700000, 0x0000000099700000, 0x0000000099800000|  0%| F|  |TAMS 0x0000000099700000, 0x0000000099700000| Untracked 
| 408|0x0000000099800000, 0x0000000099800000, 0x0000000099900000|  0%| F|  |TAMS 0x0000000099800000, 0x0000000099800000| Untracked 
| 409|0x0000000099900000, 0x0000000099900000, 0x0000000099a00000|  0%| F|  |TAMS 0x0000000099900000, 0x0000000099900000| Untracked 
| 410|0x0000000099a00000, 0x0000000099a00000, 0x0000000099b00000|  0%| F|  |TAMS 0x0000000099a00000, 0x0000000099a00000| Untracked 
| 411|0x0000000099b00000, 0x0000000099b00000, 0x0000000099c00000|  0%| F|  |TAMS 0x0000000099b00000, 0x0000000099b00000| Untracked 
| 412|0x0000000099c00000, 0x0000000099c00000, 0x0000000099d00000|  0%| F|  |TAMS 0x0000000099c00000, 0x0000000099c00000| Untracked 
| 413|0x0000000099d00000, 0x0000000099d00000, 0x0000000099e00000|  0%| F|  |TAMS 0x0000000099d00000, 0x0000000099d00000| Untracked 
| 414|0x0000000099e00000, 0x0000000099e00000, 0x0000000099f00000|  0%| F|  |TAMS 0x0000000099e00000, 0x0000000099e00000| Untracked 
| 415|0x0000000099f00000, 0x0000000099f00000, 0x000000009a000000|  0%| F|  |TAMS 0x0000000099f00000, 0x0000000099f00000| Untracked 
| 416|0x000000009a000000, 0x000000009a000000, 0x000000009a100000|  0%| F|  |TAMS 0x000000009a000000, 0x000000009a000000| Untracked 
| 417|0x000000009a100000, 0x000000009a100000, 0x000000009a200000|  0%| F|  |TAMS 0x000000009a100000, 0x000000009a100000| Untracked 
| 418|0x000000009a200000, 0x000000009a200000, 0x000000009a300000|  0%| F|  |TAMS 0x000000009a200000, 0x000000009a200000| Untracked 
| 419|0x000000009a300000, 0x000000009a300000, 0x000000009a400000|  0%| F|  |TAMS 0x000000009a300000, 0x000000009a300000| Untracked 
| 420|0x000000009a400000, 0x000000009a400000, 0x000000009a500000|  0%| F|  |TAMS 0x000000009a400000, 0x000000009a400000| Untracked 
| 421|0x000000009a500000, 0x000000009a500000, 0x000000009a600000|  0%| F|  |TAMS 0x000000009a500000, 0x000000009a500000| Untracked 
| 422|0x000000009a600000, 0x000000009a600000, 0x000000009a700000|  0%| F|  |TAMS 0x000000009a600000, 0x000000009a600000| Untracked 
| 423|0x000000009a700000, 0x000000009a700000, 0x000000009a800000|  0%| F|  |TAMS 0x000000009a700000, 0x000000009a700000| Untracked 
| 424|0x000000009a800000, 0x000000009a800000, 0x000000009a900000|  0%| F|  |TAMS 0x000000009a800000, 0x000000009a800000| Untracked 
| 425|0x000000009a900000, 0x000000009a900000, 0x000000009aa00000|  0%| F|  |TAMS 0x000000009a900000, 0x000000009a900000| Untracked 
| 426|0x000000009aa00000, 0x000000009aa00000, 0x000000009ab00000|  0%| F|  |TAMS 0x000000009aa00000, 0x000000009aa00000| Untracked 
| 427|0x000000009ab00000, 0x000000009ab04018, 0x000000009ac00000|  1%| E|  |TAMS 0x000000009ab00000, 0x000000009ab00000| Complete 
| 428|0x000000009ac00000, 0x000000009ad00000, 0x000000009ad00000|100%| E|  |TAMS 0x000000009ac00000, 0x000000009ac00000| Complete 
| 429|0x000000009ad00000, 0x000000009ae00000, 0x000000009ae00000|100%| E|CS|TAMS 0x000000009ad00000, 0x000000009ad00000| Complete 
| 430|0x000000009ae00000, 0x000000009af00000, 0x000000009af00000|100%| E|CS|TAMS 0x000000009ae00000, 0x000000009ae00000| Complete 
| 431|0x000000009af00000, 0x000000009b000000, 0x000000009b000000|100%| E|CS|TAMS 0x000000009af00000, 0x000000009af00000| Complete 
| 432|0x000000009b000000, 0x000000009b100000, 0x000000009b100000|100%| E|CS|TAMS 0x000000009b000000, 0x000000009b000000| Complete 
| 433|0x000000009b100000, 0x000000009b200000, 0x000000009b200000|100%| E|CS|TAMS 0x000000009b100000, 0x000000009b100000| Complete 

Card table byte_map: [0x000001d3f2f20000,0x000001d3f3320000] _byte_map_base: 0x000001d3f2b20000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001d3ea97da08, (CMBitMap*) 0x000001d3ea97da40
 Prev Bits: [0x000001d3f3720000, 0x000001d3f5720000)
 Next Bits: [0x000001d3f5720000, 0x000001d3f7720000)

Polling page: 0x000001d3e90b0000

Metaspace:

Usage:
  Non-class:    170.15 MB capacity,   165.94 MB ( 98%) used,     3.49 MB (  2%) free+waste,   736.69 KB ( <1%) overhead. 
      Class:     25.18 MB capacity,    22.67 MB ( 90%) used,     2.18 MB (  9%) free+waste,   342.38 KB (  1%) overhead. 
       Both:    195.33 MB capacity,   188.61 MB ( 97%) used,     5.67 MB (  3%) free+waste,     1.05 MB ( <1%) overhead. 

Virtual space:
  Non-class space:      172.00 MB reserved,     170.35 MB (>99%) committed 
      Class space:      504.00 MB reserved,      25.20 MB (  5%) committed 
             Both:      676.00 MB reserved,     195.55 MB ( 29%) committed 

Chunk freelists:
   Non-Class:  1.88 KB
       Class:  640 bytes
        Both:  2.50 KB

CodeHeap 'non-profiled nmethods': size=120000Kb used=21816Kb max_used=21816Kb free=98183Kb
 bounds [0x000001d387ad0000, 0x000001d389030000, 0x000001d38f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=63297Kb max_used=63297Kb free=56703Kb
 bounds [0x000001d3805a0000, 0x000001d3843b0000, 0x000001d387ad0000]
CodeHeap 'non-nmethods': size=5760Kb used=2529Kb max_used=2606Kb free=3230Kb
 bounds [0x000001d380000000, 0x000001d3802a0000, 0x000001d3805a0000]
 total_blobs=28593 nmethods=27375 adapters=1124
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (10 events):
Event: 474.312 Thread 0x000001d3fab0d800 nmethod 34122 0x000001d38901a590 code [0x000001d38901a700, 0x000001d38901a798]
Event: 474.765 Thread 0x000001d3fab0d800 34123       4       java.io.ObjectOutputStream::<init> (99 bytes)
Event: 474.893 Thread 0x000001d3fab0d800 nmethod 34123 0x000001d38901a890 code [0x000001d38901aae0, 0x000001d38901c398]
Event: 474.893 Thread 0x000001d3fab0d800 34124       4       java.io.ObjectOutputStream::verifySubclass (95 bytes)
Event: 474.895 Thread 0x000001d3fab0d800 nmethod 34124 0x000001d38901ce10 code [0x000001d38901cfa0, 0x000001d38901d038]
Event: 478.799 Thread 0x000001d3fab0d800 34125   !   4       sun.security.ssl.SSLSocketImpl$AppInputStream::read (645 bytes)
Event: 478.841 Thread 0x000001d3fab0d800 nmethod 34125 0x000001d38901d110 code [0x000001d38901d3a0, 0x000001d38901e5c0]
Event: 480.372 Thread 0x000001d3fab0d800 34126       4       org.apache.http.impl.io.ContentLengthInputStream::read (148 bytes)
Event: 480.392 Thread 0x000001d3fab0d800 nmethod 34126 0x000001d38901f490 code [0x000001d38901f660, 0x000001d38901fd78]
Event: 480.564 Thread 0x000001d3fab0d800 34127       4       org.gradle.internal.resource.transfer.AbstractProgressLoggingHandler$ProgressLoggingInputStream::read (27 bytes)

GC Heap History (10 events):
Event: 372.668 GC heap before
{Heap before GC invocations=136 (full 0):
 garbage-first heap   total 444416K, used 385086K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 156 young (159744K), 2 survivors (2048K)
 Metaspace       used 190358K, capacity 197025K, committed 197552K, reserved 688128K
  class space    used 23023K, capacity 25513K, committed 25672K, reserved 516096K
}
Event: 372.677 GC heap after
{Heap after GC invocations=137 (full 0):
 garbage-first heap   total 444416K, used 227666K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 190358K, capacity 197025K, committed 197552K, reserved 688128K
  class space    used 23023K, capacity 25513K, committed 25672K, reserved 516096K
}
Event: 406.329 GC heap before
{Heap before GC invocations=137 (full 0):
 garbage-first heap   total 444416K, used 387410K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 158 young (161792K), 2 survivors (2048K)
 Metaspace       used 190722K, capacity 197439K, committed 197808K, reserved 690176K
  class space    used 23045K, capacity 25557K, committed 25672K, reserved 516096K
}
Event: 406.339 GC heap after
{Heap after GC invocations=138 (full 0):
 garbage-first heap   total 444416K, used 232389K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 190722K, capacity 197439K, committed 197808K, reserved 690176K
  class space    used 23045K, capacity 25557K, committed 25672K, reserved 516096K
}
Event: 406.866 GC heap before
{Heap before GC invocations=138 (full 0):
 garbage-first heap   total 444416K, used 387013K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 157 young (160768K), 7 survivors (7168K)
 Metaspace       used 190757K, capacity 197444K, committed 197808K, reserved 690176K
  class space    used 23045K, capacity 25558K, committed 25672K, reserved 516096K
}
Event: 406.875 GC heap after
{Heap after GC invocations=139 (full 0):
 garbage-first heap   total 444416K, used 233442K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 190757K, capacity 197444K, committed 197808K, reserved 690176K
  class space    used 23045K, capacity 25558K, committed 25672K, reserved 516096K
}
Event: 444.787 GC heap before
{Heap before GC invocations=139 (full 0):
 garbage-first heap   total 444416K, used 387042K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 159 young (162816K), 8 survivors (8192K)
 Metaspace       used 192611K, capacity 199545K, committed 199728K, reserved 690176K
  class space    used 23212K, capacity 25786K, committed 25800K, reserved 516096K
}
Event: 444.803 GC heap after
{Heap after GC invocations=140 (full 0):
 garbage-first heap   total 444416K, used 237696K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 192611K, capacity 199545K, committed 199728K, reserved 690176K
  class space    used 23212K, capacity 25786K, committed 25800K, reserved 516096K
}
Event: 479.805 GC heap before
{Heap before GC invocations=140 (full 0):
 garbage-first heap   total 444416K, used 385152K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 157 young (160768K), 12 survivors (12288K)
 Metaspace       used 193135K, capacity 200020K, committed 200240K, reserved 692224K
  class space    used 23215K, capacity 25787K, committed 25800K, reserved 516096K
}
Event: 479.819 GC heap after
{Heap after GC invocations=141 (full 0):
 garbage-first heap   total 444416K, used 239176K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 193135K, capacity 200020K, committed 200240K, reserved 692224K
  class space    used 23215K, capacity 25787K, committed 25800K, reserved 516096K
}

Deoptimization events (10 events):
Event: 453.011 Thread 0x000001d3fb901800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001d388ea09e0 method=com.sun.org.apache.xerces.internal.impl.XMLEntityScanner.scanData(Ljava/lang/String;Lcom/sun/org/apache/xerces/internal/util/XMLStringBuffer;I)Z @ 531 c2
Event: 456.107 Thread 0x000001d3fb901800 Uncommon trap: reason=loop_limit_check action=maybe_recompile pc=0x000001d388f972f8 method=com.sun.crypto.provider.ChaCha20Cipher.xor([BI[BI[BII)V @ 4 c2
Event: 459.856 Thread 0x000001d3fb901800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001d388bf1f70 method=com.sun.org.apache.xerces.internal.impl.XMLDocumentFragmentScannerImpl$FragmentContentDriver.next()I @ 1049 c2
Event: 459.864 Thread 0x000001d3fb901800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001d388bbe680 method=com.sun.org.apache.xerces.internal.impl.XMLEntityScanner.scanLiteral(ILcom/sun/org/apache/xerces/internal/xni/XMLString;Z)I @ 181 c2
Event: 459.865 Thread 0x000001d3fb901800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001d388f9bc60 method=com.sun.org.apache.xerces.internal.xni.XMLString.toString()Ljava/lang/String; @ 4 c2
Event: 460.103 Thread 0x000001d3fb901800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001d3885a1ca0 method=org.apache.ivy.core.IvyPatternHelper.substituteVariables(Ljava/lang/String;Lorg/apache/ivy/core/settings/IvyVariableContainer;Ljava/util/Stack;)Ljava/lang/String; @ 63 c2
Event: 460.115 Thread 0x000001d3a306b800 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001d3887fddf0 method=com.google.common.cache.LocalCache$Segment.get(Ljava/lang/Object;ILcom/google/common/cache/CacheLoader;)Ljava/lang/Object; @ 108 c2
Event: 460.125 Thread 0x000001d3fb901800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001d388fbd790 method=com.sun.org.apache.xerces.internal.xni.XMLString.toString()Ljava/lang/String; @ 4 c2
Event: 471.110 Thread 0x000001d3fb901800 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001d388c6d7c0 method=kotlin.jvm.internal.Intrinsics.compare(II)I @ 2 c2
Event: 471.123 Thread 0x000001d3fb901800 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000001d387b0d37c method=java.util.stream.StreamOpFlag.fromCharacteristics(Ljava/util/Spliterator;)I @ 14 c2

Classes redefined (0 events):
No events

Internal exceptions (10 events):
Event: 471.340 Thread 0x000001d391bfc800 Exception <a 'sun/nio/fs/WindowsException'{0x00000000951784d0}> (0x00000000951784d0) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 611]
Event: 471.342 Thread 0x000001d391bfc800 Exception <a 'sun/nio/fs/WindowsException'{0x000000009517b2d0}> (0x000000009517b2d0) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 611]
Event: 471.342 Thread 0x000001d391bfc800 Exception <a 'sun/nio/fs/WindowsException'{0x000000009517e028}> (0x000000009517e028) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 611]
Event: 471.619 Thread 0x000001d38f95b800 Exception <a 'java/net/SocketTimeoutException'{0x0000000094fb9ed8}: Read timed out> (0x0000000094fb9ed8) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 632]
Event: 471.619 Thread 0x000001d38f959000 Exception <a 'java/net/SocketTimeoutException'{0x0000000094f277f8}: Read timed out> (0x0000000094f277f8) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 632]
Event: 471.619 Thread 0x000001d38fea9000 Exception <a 'java/net/SocketTimeoutException'{0x00000000950fdc48}: Read timed out> (0x00000000950fdc48) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 632]
Event: 471.619 Thread 0x000001d391bfc800 Exception <a 'java/net/SocketTimeoutException'{0x0000000094ff4808}: Read timed out> (0x0000000094ff4808) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 632]
Event: 471.620 Thread 0x000001d3916f6800 Exception <a 'java/net/SocketTimeoutException'{0x0000000094f598f8}: Read timed out> (0x0000000094f598f8) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 632]
Event: 471.620 Thread 0x000001d38fead000 Exception <a 'java/net/SocketTimeoutException'{0x0000000094f0b5e8}: Read timed out> (0x0000000094f0b5e8) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 632]
Event: 471.619 Thread 0x000001d38feab800 Exception <a 'java/net/SocketTimeoutException'{0x0000000094fe08e0}: Read timed out> (0x0000000094fe08e0) thrown at [./open/src/hotspot/share/prims/jni.cpp, line 632]

Events (10 events):
Event: 471.238 Executing VM operation: RevokeBias done
Event: 471.276 Thread 0x000001d391bf5800 Thread added: 0x000001d391bf5800
Event: 471.340 Thread 0x000001d391bfc800 DEOPT PACKING pc=0x000001d38343128d sp=0x00000025c58fd180
Event: 471.340 Thread 0x000001d391bfc800 DEOPT UNPACKING pc=0x000001d380026d5e sp=0x00000025c58fc6d8 mode 0
Event: 471.585 Executing VM operation: RevokeBias
Event: 471.586 Executing VM operation: RevokeBias done
Event: 471.588 Executing VM operation: RevokeBias
Event: 471.588 Executing VM operation: RevokeBias done
Event: 479.805 Executing VM operation: G1CollectForAllocation
Event: 479.819 Executing VM operation: G1CollectForAllocation done


Dynamic libraries:
0x00007ff659a60000 - 0x00007ff659a70000 	C:\Program Files\Java\jdk-11.0.25\bin\java.exe
0x00007fff7f780000 - 0x00007fff7f9e5000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007fff7ebe0000 - 0x00007fff7eca9000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007fff7ccf0000 - 0x00007fff7d0d8000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007fff7d340000 - 0x00007fff7d48b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007fff6c370000 - 0x00007fff6c389000 	C:\Program Files\Java\jdk-11.0.25\bin\jli.dll
0x00007fff6c250000 - 0x00007fff6c26b000 	C:\Program Files\Java\jdk-11.0.25\bin\VCRUNTIME140.dll
0x00007fff7f490000 - 0x00007fff7f543000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007fff7ed90000 - 0x00007fff7ee39000 	C:\WINDOWS\System32\msvcrt.dll
0x00007fff7f3d0000 - 0x00007fff7f476000 	C:\WINDOWS\System32\sechost.dll
0x00007fff7d520000 - 0x00007fff7d635000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007fff7f1a0000 - 0x00007fff7f36a000 	C:\WINDOWS\System32\USER32.dll
0x00007fff7d260000 - 0x00007fff7d287000 	C:\WINDOWS\System32\win32u.dll
0x00007fff660e0000 - 0x00007fff6637a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007fff7f710000 - 0x00007fff7f73b000 	C:\WINDOWS\System32\GDI32.dll
0x00007fff7c8d0000 - 0x00007fff7ca07000 	C:\WINDOWS\System32\gdi32full.dll
0x00007fff7d290000 - 0x00007fff7d333000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007fff6de40000 - 0x00007fff6de4b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007fff7ecd0000 - 0x00007fff7ed00000 	C:\WINDOWS\System32\IMM32.DLL
0x00007fff764a0000 - 0x00007fff764ac000 	C:\Program Files\Java\jdk-11.0.25\bin\vcruntime140_1.dll
0x00007fff51890000 - 0x00007fff5191e000 	C:\Program Files\Java\jdk-11.0.25\bin\msvcp140.dll
0x00007fff0ec90000 - 0x00007fff0f7f3000 	C:\Program Files\Java\jdk-11.0.25\bin\server\jvm.dll
0x00007fff7dbf0000 - 0x00007fff7dbf8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007fff666f0000 - 0x00007fff66725000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007fff58c00000 - 0x00007fff58c0a000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007fff7eeb0000 - 0x00007fff7ef24000 	C:\WINDOWS\System32\WS2_32.dll
0x00007fff7b7b0000 - 0x00007fff7b7cb000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007fff76440000 - 0x00007fff76450000 	C:\Program Files\Java\jdk-11.0.25\bin\verify.dll
0x00007fff76bf0000 - 0x00007fff76e31000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007fff7d860000 - 0x00007fff7dbe5000 	C:\WINDOWS\System32\combase.dll
0x00007fff7f0b0000 - 0x00007fff7f191000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007fff61080000 - 0x00007fff610b9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007fff7ca10000 - 0x00007fff7caa9000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007fff67cc0000 - 0x00007fff67ce8000 	C:\Program Files\Java\jdk-11.0.25\bin\java.dll
0x00007fff6f470000 - 0x00007fff6f47a000 	C:\Program Files\Java\jdk-11.0.25\bin\jimage.dll
0x00007fff6c240000 - 0x00007fff6c24e000 	C:\Program Files\Java\jdk-11.0.25\bin\instrument.dll
0x00007fff6bdd0000 - 0x00007fff6bde7000 	C:\Program Files\Java\jdk-11.0.25\bin\zip.dll
0x00007fff7e140000 - 0x00007fff7e882000 	C:\WINDOWS\System32\SHELL32.dll
0x00007fff7d0e0000 - 0x00007fff7d254000 	C:\WINDOWS\System32\wintypes.dll
0x00007fff7a590000 - 0x00007fff7ade8000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007fff7f580000 - 0x00007fff7f671000 	C:\WINDOWS\System32\SHCORE.dll
0x00007fff7d640000 - 0x00007fff7d6aa000 	C:\WINDOWS\System32\shlwapi.dll
0x00007fff7c7e0000 - 0x00007fff7c80f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007fff6bbd0000 - 0x00007fff6bbe9000 	C:\Program Files\Java\jdk-11.0.25\bin\net.dll
0x00007fff76ff0000 - 0x00007fff7710e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007fff7bd20000 - 0x00007fff7bd8a000 	C:\WINDOWS\system32\mswsock.dll
0x00007fff67d80000 - 0x00007fff67d92000 	C:\Program Files\Java\jdk-11.0.25\bin\nio.dll
0x00007fff6c390000 - 0x00007fff6c3b7000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00000000718d0000 - 0x0000000071943000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007fff6c200000 - 0x00007fff6c209000 	C:\Program Files\Java\jdk-11.0.25\bin\management.dll
0x00007fff6bc90000 - 0x00007fff6bc9b000 	C:\Program Files\Java\jdk-11.0.25\bin\management_ext.dll
0x00007fff7bfd0000 - 0x00007fff7bfeb000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007fff7b710000 - 0x00007fff7b74a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007fff7bdc0000 - 0x00007fff7bdeb000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007fff7c7b0000 - 0x00007fff7c7d6000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007fff7bff0000 - 0x00007fff7bffc000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007fff7b150000 - 0x00007fff7b183000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007fff7f480000 - 0x00007fff7f48a000 	C:\WINDOWS\System32\NSI.dll
0x00007fff6dd30000 - 0x00007fff6dd4f000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007fff6dd00000 - 0x00007fff6dd25000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007fff7b1e0000 - 0x00007fff7b307000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x0000000071850000 - 0x00000000718c3000 	C:\Users\<USER>\AppData\Local\Temp\native-platform11066530273439840867dir\gradle-fileevents.dll
0x00007fff57dc0000 - 0x00007fff57dd8000 	C:\WINDOWS\system32\napinsp.dll
0x00007fff57de0000 - 0x00007fff57df2000 	C:\WINDOWS\System32\winrnr.dll
0x00007fff57d80000 - 0x00007fff57db0000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007fff6de50000 - 0x00007fff6de70000 	C:\WINDOWS\system32\wshbth.dll
0x00007fff6d8f0000 - 0x00007fff6d8fb000 	C:\Windows\System32\rasadhlp.dll
0x00007fff6d540000 - 0x00007fff6d5c6000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007fff66b80000 - 0x00007fff66ba7000 	C:\Program Files\Java\jdk-11.0.25\bin\sunec.dll
0x00007fff6b950000 - 0x00007fff6b95e000 	C:\Program Files\Java\jdk-11.0.25\bin\sunmscapi.dll
0x00007fff7cb70000 - 0x00007fff7cce7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007fff7c1f0000 - 0x00007fff7c220000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007fff7c1a0000 - 0x00007fff7c1df000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007fff799e0000 - 0x00007fff79a7e000 	C:\WINDOWS\system32\apphelp.dll
0x00007fff65600000 - 0x00007fff65607000 	C:\Program Files\Java\jdk-11.0.25\bin\rmi.dll
0x00007fff7b8d0000 - 0x00007fff7b906000 	C:\WINDOWS\SYSTEM32\ntmarta.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Java\jdk-11.0.25\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;C:\Program Files\Java\jdk-11.0.25\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform11066530273439840867dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=512m -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 528482304                                 {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxMetaspaceSize                         = 536870912                                 {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5836300                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122910970                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122910970                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
     bool UseCompressedClassPointers               = true                                 {lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                                 {lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-11.0.25
PATH=C:\Gradle\gradle-8.13\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files\Java\jdk-11.0.25\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\SqlCmd\;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\redist\intel64\compiler;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Intel\WiFi\bin\;C:\Program Files\Common Files\Intel\WirelessCommon\;C:\ProgramData\chocolatey\bin;C:\Python312\Scripts;C:\Python312;C:\Program Files\dotnet\;C:\xampp\php\php.exe;C:\php;C:\tools\php84;C:\xampp\mysql\bin;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\ncat;C:\curl;C:\Users\<USER>\AppData\Roaming\npm\node_modules;C:\Users\<USER>\AppData\Local\nvm;C:\nvm4w\nodejs;C:\Program Files\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundatio;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Git\cmd;C:\Program Files\Gradle\gradle-8.13\bin;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\Scripts;C:\Users\<USER>\AppData\Roaming\npm;C:\Program Files (x86)\Nmap;C:\xampp\mysql\b;C:\Users\<USER>\AppData\Local\Programs\mongosh\;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\MongoDB\Server\7.0\bin;C:\Program Files\MongoDB\Server\8.0\bin;;C:\PostgreSQL\16\bin;C:\Program Files\SqlCmd\;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Python312\Scripts\;C:\Python312\;C:\Program Files (x86)\Com
USERNAME=Sheldon
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 10, GenuineIntel



---------------  S Y S T E M  ---------------

OS: Windows 11 , 64 bit Build 26100 (10.0.26100.4202)

CPU:total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 142 stepping 10 microcode 0xf6, cmov, cx8, fxsr, mmx, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, avx, avx2, aes, clmul, erms, rtm, 3dnowpref, lzcnt, ht, tsc, tscinvbit, bmi1, bmi2, adx, fma

Memory: 4k page, system-wide physical 16222M (1063M free)
TotalPageFile size 21072M (AvailPageFile size 1M)
current process WorkingSet (physical memory assigned to process): 953M, peak: 960M
current process commit charge ("private bytes"): 980M, peak: 989M

vm_info: Java HotSpot(TM) 64-Bit Server VM (11.0.25+9-LTS-256) for windows-amd64 JRE (11.0.25+9-LTS-256), built on Sep 30 2024 06:30:20 by "mach5one" with MS VC++ 17.6 (VS2022)

END.
